"""
Enhanced Data Compression Manager for Lionaire platform.
Phase 4.1.1 - Data Compression Enhancement.

Features:
- Multiple compression algorithms with automatic selection
- Memory compression for large datasets
- Data type optimization
- Compression ratio monitoring
- Performance benchmarking
"""

import pandas as pd
import numpy as np
import json
import time
import threading
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
import io
import gzip
import lz4.frame
import brotli

from ..config.logging_config import get_logger
from ..config.user_settings import get_user_settings
from ..core.utils import estimate_memory_usage

logger = get_logger(__name__)


@dataclass
class CompressionResult:
    """Result of compression operation."""
    original_size: int
    compressed_size: int
    compression_ratio: float
    compression_time: float
    decompression_time: float
    algorithm: str
    success: bool
    error_message: Optional[str] = None


@dataclass
class CompressionBenchmark:
    """Benchmark results for compression algorithms."""
    algorithm: str
    avg_compression_ratio: float
    avg_compression_time: float
    avg_decompression_time: float
    total_operations: int
    success_rate: float


class CompressionManager:
    """
    Enhanced compression manager with multiple algorithms and optimization.
    """
    
    def __init__(self):
        """Initialize Compression Manager."""
        self.user_settings = get_user_settings()
        
        # Supported compression algorithms
        self.algorithms = {
            'lz4': self._compress_lz4,
            'snappy': self._compress_snappy,
            'gzip': self._compress_gzip,
            'brotli': self._compress_brotli,
            'none': self._compress_none
        }
        
        # Decompression functions
        self.decompressors = {
            'lz4': self._decompress_lz4,
            'snappy': self._decompress_snappy,
            'gzip': self._decompress_gzip,
            'brotli': self._decompress_brotli,
            'none': self._decompress_none
        }
        
        # Performance tracking
        self.benchmarks: Dict[str, List[CompressionResult]] = {}
        self._lock = threading.RLock()
        
        # Algorithm characteristics
        self.algorithm_profiles = {
            'lz4': {'speed': 'fastest', 'ratio': 'good', 'cpu': 'low'},
            'snappy': {'speed': 'fast', 'ratio': 'good', 'cpu': 'low'},
            'gzip': {'speed': 'slow', 'ratio': 'best', 'cpu': 'high'},
            'brotli': {'speed': 'slowest', 'ratio': 'excellent', 'cpu': 'highest'},
            'none': {'speed': 'instant', 'ratio': 'none', 'cpu': 'none'}
        }
        
        logger.info("Enhanced Compression Manager initialized")
    
    def compress_dataframe(self, df: pd.DataFrame, algorithm: Optional[str] = None) -> Tuple[bytes, CompressionResult]:
        """
        Compress DataFrame with optimal algorithm selection.
        
        Args:
            df: DataFrame to compress
            algorithm: Specific algorithm to use (optional)
            
        Returns:
            Tuple of (compressed_data, compression_result)
        """
        try:
            # Optimize DataFrame before compression
            optimized_df = self._optimize_dataframe(df)
            
            # Select algorithm if not specified
            if algorithm is None:
                algorithm = self._select_optimal_algorithm(optimized_df)
            
            # Convert DataFrame to bytes
            buffer = io.BytesIO()
            optimized_df.to_parquet(buffer, compression='none')
            original_data = buffer.getvalue()
            original_size = len(original_data)
            
            # Compress data
            start_time = time.time()
            compressed_data = self.algorithms[algorithm](original_data)
            compression_time = time.time() - start_time
            
            # Test decompression for validation
            start_time = time.time()
            decompressed_data = self.decompressors[algorithm](compressed_data)
            decompression_time = time.time() - start_time
            
            # Verify data integrity
            if decompressed_data != original_data:
                raise ValueError("Data integrity check failed")
            
            compressed_size = len(compressed_data)
            compression_ratio = original_size / compressed_size if compressed_size > 0 else 1.0
            
            result = CompressionResult(
                original_size=original_size,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                compression_time=compression_time,
                decompression_time=decompression_time,
                algorithm=algorithm,
                success=True
            )
            
            # Record benchmark
            self._record_benchmark(result)
            
            logger.debug(f"Compressed DataFrame: {original_size} → {compressed_size} bytes "
                        f"({compression_ratio:.2f}x) using {algorithm}")
            
            return compressed_data, result
            
        except Exception as e:
            logger.error(f"Error compressing DataFrame: {e}")
            result = CompressionResult(
                original_size=0,
                compressed_size=0,
                compression_ratio=1.0,
                compression_time=0.0,
                decompression_time=0.0,
                algorithm=algorithm or 'unknown',
                success=False,
                error_message=str(e)
            )
            return b'', result
    
    def decompress_dataframe(self, compressed_data: bytes, algorithm: str) -> Tuple[Optional[pd.DataFrame], bool]:
        """
        Decompress data back to DataFrame.
        
        Args:
            compressed_data: Compressed data bytes
            algorithm: Compression algorithm used
            
        Returns:
            Tuple of (DataFrame, success)
        """
        try:
            # Decompress data
            decompressed_data = self.decompressors[algorithm](compressed_data)
            
            # Convert back to DataFrame
            buffer = io.BytesIO(decompressed_data)
            df = pd.read_parquet(buffer)
            
            logger.debug(f"Decompressed DataFrame: {len(compressed_data)} → {len(decompressed_data)} bytes")
            return df, True
            
        except Exception as e:
            logger.error(f"Error decompressing DataFrame: {e}")
            return None, False
    
    def _optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame data types for better compression.
        
        Args:
            df: Original DataFrame
            
        Returns:
            Optimized DataFrame
        """
        try:
            optimized_df = df.copy()
            
            # Optimize numeric columns
            for col in optimized_df.select_dtypes(include=[np.number]).columns:
                if col in ['open', 'high', 'low', 'close']:
                    # For price data, check if float32 precision is sufficient
                    if optimized_df[col].max() < 1e6 and optimized_df[col].min() > -1e6:
                        # Check precision loss
                        float32_data = optimized_df[col].astype(np.float32)
                        precision_loss = np.abs(optimized_df[col] - float32_data).max()
                        if precision_loss < 1e-4:  # Less than 0.0001 precision loss
                            optimized_df[col] = float32_data
                            logger.debug(f"Optimized {col} to float32 (precision loss: {precision_loss:.6f})")
                
                elif col == 'volume':
                    # Volume can often be stored as smaller integer types
                    max_vol = optimized_df[col].max()
                    if max_vol < np.iinfo(np.int32).max:
                        optimized_df[col] = optimized_df[col].astype(np.int32)
                        logger.debug(f"Optimized {col} to int32")
            
            # Check for categorical data (though unlikely in OHLCV data)
            for col in optimized_df.select_dtypes(include=['object']).columns:
                unique_ratio = optimized_df[col].nunique() / len(optimized_df)
                if unique_ratio < 0.5:  # Less than 50% unique values
                    optimized_df[col] = optimized_df[col].astype('category')
                    logger.debug(f"Optimized {col} to category")
            
            return optimized_df
            
        except Exception as e:
            logger.warning(f"Error optimizing DataFrame: {e}")
            return df
    
    def _select_optimal_algorithm(self, df: pd.DataFrame) -> str:
        """
        Select optimal compression algorithm based on data characteristics.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Optimal algorithm name
        """
        try:
            # Get user preference
            user_preference = self.user_settings.get_setting('cache.cache_compression', 'lz4')
            
            # Analyze data characteristics
            data_size = estimate_memory_usage(df).get('total_mb', 0)
            
            # For small data (< 1MB), use fastest algorithm
            if data_size < 1:
                return 'lz4'
            
            # For medium data (1-10MB), balance speed and compression
            elif data_size < 10:
                return user_preference if user_preference in self.algorithms else 'lz4'
            
            # For large data (> 10MB), prioritize compression ratio
            else:
                if user_preference == 'gzip' or user_preference == 'brotli':
                    return user_preference
                else:
                    return 'gzip'  # Better compression for large data
            
        except Exception as e:
            logger.warning(f"Error selecting algorithm: {e}")
            return 'lz4'  # Safe default
    
    # Compression algorithm implementations
    def _compress_lz4(self, data: bytes) -> bytes:
        """Compress using LZ4."""
        return lz4.frame.compress(data)
    
    def _decompress_lz4(self, data: bytes) -> bytes:
        """Decompress LZ4 data."""
        return lz4.frame.decompress(data)
    
    def _compress_snappy(self, data: bytes) -> bytes:
        """Compress using Snappy."""
        try:
            import snappy
            return snappy.compress(data)
        except ImportError:
            logger.warning("Snappy not available, falling back to LZ4")
            return self._compress_lz4(data)
    
    def _decompress_snappy(self, data: bytes) -> bytes:
        """Decompress Snappy data."""
        try:
            import snappy
            return snappy.decompress(data)
        except ImportError:
            return self._decompress_lz4(data)
    
    def _compress_gzip(self, data: bytes) -> bytes:
        """Compress using Gzip."""
        return gzip.compress(data, compresslevel=6)
    
    def _decompress_gzip(self, data: bytes) -> bytes:
        """Decompress Gzip data."""
        return gzip.decompress(data)
    
    def _compress_brotli(self, data: bytes) -> bytes:
        """Compress using Brotli."""
        return brotli.compress(data, quality=6)
    
    def _decompress_brotli(self, data: bytes) -> bytes:
        """Decompress Brotli data."""
        return brotli.decompress(data)
    
    def _compress_none(self, data: bytes) -> bytes:
        """No compression."""
        return data
    
    def _decompress_none(self, data: bytes) -> bytes:
        """No decompression."""
        return data
    
    def _record_benchmark(self, result: CompressionResult):
        """Record benchmark result."""
        try:
            with self._lock:
                if result.algorithm not in self.benchmarks:
                    self.benchmarks[result.algorithm] = []
                
                self.benchmarks[result.algorithm].append(result)
                
                # Keep only last 100 results per algorithm
                if len(self.benchmarks[result.algorithm]) > 100:
                    self.benchmarks[result.algorithm] = self.benchmarks[result.algorithm][-100:]
                    
        except Exception as e:
            logger.error(f"Error recording benchmark: {e}")
    
    def get_compression_benchmarks(self) -> Dict[str, CompressionBenchmark]:
        """Get compression performance benchmarks."""
        try:
            with self._lock:
                benchmarks = {}
                
                for algorithm, results in self.benchmarks.items():
                    if not results:
                        continue
                    
                    successful_results = [r for r in results if r.success]
                    if not successful_results:
                        continue
                    
                    avg_ratio = sum(r.compression_ratio for r in successful_results) / len(successful_results)
                    avg_comp_time = sum(r.compression_time for r in successful_results) / len(successful_results)
                    avg_decomp_time = sum(r.decompression_time for r in successful_results) / len(successful_results)
                    success_rate = len(successful_results) / len(results) * 100
                    
                    benchmarks[algorithm] = CompressionBenchmark(
                        algorithm=algorithm,
                        avg_compression_ratio=avg_ratio,
                        avg_compression_time=avg_comp_time,
                        avg_decompression_time=avg_decomp_time,
                        total_operations=len(results),
                        success_rate=success_rate
                    )
                
                return benchmarks
                
        except Exception as e:
            logger.error(f"Error getting benchmarks: {e}")
            return {}
    
    def get_algorithm_recommendation(self, data_size_mb: float, priority: str = 'balanced') -> str:
        """
        Get algorithm recommendation based on data size and priority.
        
        Args:
            data_size_mb: Data size in MB
            priority: 'speed', 'compression', or 'balanced'
            
        Returns:
            Recommended algorithm
        """
        try:
            if priority == 'speed':
                return 'lz4'
            elif priority == 'compression':
                if data_size_mb > 50:
                    return 'brotli'
                else:
                    return 'gzip'
            else:  # balanced
                if data_size_mb < 1:
                    return 'lz4'
                elif data_size_mb < 10:
                    return 'snappy'
                else:
                    return 'gzip'
                    
        except Exception as e:
            logger.error(f"Error getting recommendation: {e}")
            return 'lz4'
