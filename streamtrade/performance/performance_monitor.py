"""
Enhanced Performance Monitor for Lionaire platform.
Phase 4.1 - Performance Optimization.

Features:
- Real-time performance monitoring
- Performance dashboard
- Automatic optimization recommendations
- Integration with all performance modules
"""

import time
import threading
import psutil
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

from ..config.logging_config import get_logger
from .compression_manager import CompressionManager
from .memory_profiler import MemoryProfiler
from .parallel_processor import ParallelProcessor

logger = get_logger(__name__)


@dataclass
class PerformanceSnapshot:
    """Complete performance snapshot."""
    timestamp: datetime
    
    # System metrics
    cpu_percent: float
    memory_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    
    # Application metrics
    active_threads: int
    dataframe_count: int
    dataframe_memory_mb: float
    cache_hit_rate: float
    
    # Performance metrics
    avg_indicator_calc_time: float
    avg_data_load_time: float
    avg_compression_ratio: float
    parallel_tasks_active: int
    
    # User experience metrics
    ui_response_time: float
    chart_render_time: float
    data_export_time: float


@dataclass
class PerformanceAlert:
    """Performance alert."""
    timestamp: datetime
    severity: str  # 'info', 'warning', 'critical'
    category: str  # 'memory', 'cpu', 'disk', 'performance'
    message: str
    metric_value: float
    threshold: float
    recommendation: str


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation."""
    category: str
    priority: str  # 'high', 'medium', 'low'
    title: str
    description: str
    potential_improvement: str
    implementation_effort: str  # 'low', 'medium', 'high'
    action_items: List[str]


class PerformanceMonitor:
    """
    Enhanced performance monitor with comprehensive tracking and optimization.
    """
    
    def __init__(self, monitoring_interval: int = 30):
        """
        Initialize Performance Monitor.
        
        Args:
            monitoring_interval: Monitoring interval in seconds
        """
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        
        # Performance modules
        self.compression_manager = CompressionManager()
        self.memory_profiler = MemoryProfiler(monitoring_interval)
        self.parallel_processor = ParallelProcessor()
        
        # Data storage
        self.snapshots: List[PerformanceSnapshot] = []
        self.alerts: List[PerformanceAlert] = []
        self.max_snapshots = 1000
        self.max_alerts = 500
        
        # Performance thresholds
        self.thresholds = {
            'cpu_warning': 70,
            'cpu_critical': 85,
            'memory_warning': 75,
            'memory_critical': 90,
            'disk_io_warning': 100,  # MB/s
            'disk_io_critical': 200,
            'response_time_warning': 2.0,  # seconds
            'response_time_critical': 5.0,
            'cache_hit_rate_warning': 60,  # %
            'cache_hit_rate_critical': 40
        }
        
        # Thread safety
        self._lock = threading.RLock()
        self._monitor_thread: Optional[threading.Thread] = None
        
        # Performance tracking
        self._operation_times: Dict[str, List[float]] = {}
        self._last_disk_io = None
        
        logger.info("Enhanced Performance Monitor initialized")
    
    def start_monitoring(self):
        """Start comprehensive performance monitoring."""
        try:
            if self.is_monitoring:
                logger.warning("Performance monitoring already running")
                return
            
            # Start sub-monitors
            self.memory_profiler.start_monitoring()
            self.parallel_processor.start()
            
            # Start main monitoring
            self.is_monitoring = True
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            
            logger.info("Enhanced performance monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting performance monitoring: {e}")
            self.is_monitoring = False
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        try:
            self.is_monitoring = False
            
            # Stop sub-monitors
            self.memory_profiler.stop_monitoring()
            self.parallel_processor.stop()
            
            # Stop main monitoring
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            
            logger.info("Performance monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping performance monitoring: {e}")
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                snapshot = self._take_performance_snapshot()
                
                with self._lock:
                    self.snapshots.append(snapshot)
                    
                    # Keep only recent snapshots
                    if len(self.snapshots) > self.max_snapshots:
                        self.snapshots = self.snapshots[-self.max_snapshots:]
                
                # Check for performance issues
                self._check_performance_thresholds(snapshot)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _take_performance_snapshot(self) -> PerformanceSnapshot:
        """Take a comprehensive performance snapshot."""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            if self._last_disk_io:
                disk_read_mb = (disk_io.read_bytes - self._last_disk_io.read_bytes) / 1024 / 1024
                disk_write_mb = (disk_io.write_bytes - self._last_disk_io.write_bytes) / 1024 / 1024
            else:
                disk_read_mb = disk_write_mb = 0
            self._last_disk_io = disk_io
            
            # Application metrics
            active_threads = threading.active_count()
            
            # Memory profiler data
            memory_data = self.memory_profiler.get_current_memory_usage()
            dataframe_count = memory_data.get('dataframes', {}).get('count', 0)
            dataframe_memory_mb = memory_data.get('dataframes', {}).get('memory_mb', 0)
            
            # Compression metrics
            compression_benchmarks = self.compression_manager.get_compression_benchmarks()
            avg_compression_ratio = 0
            if compression_benchmarks:
                ratios = [b.avg_compression_ratio for b in compression_benchmarks.values()]
                avg_compression_ratio = sum(ratios) / len(ratios) if ratios else 0
            
            # Parallel processing metrics
            parallel_metrics = self.parallel_processor.get_performance_metrics()
            
            # Performance metrics (from tracked operations)
            avg_indicator_calc_time = self._get_avg_operation_time('indicator_calculation')
            avg_data_load_time = self._get_avg_operation_time('data_loading')
            ui_response_time = self._get_avg_operation_time('ui_response')
            chart_render_time = self._get_avg_operation_time('chart_rendering')
            data_export_time = self._get_avg_operation_time('data_export')
            
            snapshot = PerformanceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                active_threads=active_threads,
                dataframe_count=dataframe_count,
                dataframe_memory_mb=dataframe_memory_mb,
                cache_hit_rate=0,  # TODO: Implement cache hit rate tracking
                avg_indicator_calc_time=avg_indicator_calc_time,
                avg_data_load_time=avg_data_load_time,
                avg_compression_ratio=avg_compression_ratio,
                parallel_tasks_active=parallel_metrics.active_workers,
                ui_response_time=ui_response_time,
                chart_render_time=chart_render_time,
                data_export_time=data_export_time
            )
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error taking performance snapshot: {e}")
            # Return empty snapshot
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=0, memory_percent=0, disk_io_read_mb=0, disk_io_write_mb=0,
                active_threads=0, dataframe_count=0, dataframe_memory_mb=0, cache_hit_rate=0,
                avg_indicator_calc_time=0, avg_data_load_time=0, avg_compression_ratio=0,
                parallel_tasks_active=0, ui_response_time=0, chart_render_time=0, data_export_time=0
            )
    
    def _get_avg_operation_time(self, operation: str) -> float:
        """Get average operation time."""
        try:
            if operation in self._operation_times and self._operation_times[operation]:
                times = self._operation_times[operation]
                return sum(times) / len(times)
            return 0.0
        except:
            return 0.0
    
    def _check_performance_thresholds(self, snapshot: PerformanceSnapshot):
        """Check performance thresholds and generate alerts."""
        try:
            alerts = []
            
            # CPU usage
            if snapshot.cpu_percent > self.thresholds['cpu_critical']:
                alerts.append(PerformanceAlert(
                    timestamp=snapshot.timestamp,
                    severity='critical',
                    category='cpu',
                    message=f"Critical CPU usage: {snapshot.cpu_percent:.1f}%",
                    metric_value=snapshot.cpu_percent,
                    threshold=self.thresholds['cpu_critical'],
                    recommendation="Consider reducing parallel processing or optimizing algorithms"
                ))
            elif snapshot.cpu_percent > self.thresholds['cpu_warning']:
                alerts.append(PerformanceAlert(
                    timestamp=snapshot.timestamp,
                    severity='warning',
                    category='cpu',
                    message=f"High CPU usage: {snapshot.cpu_percent:.1f}%",
                    metric_value=snapshot.cpu_percent,
                    threshold=self.thresholds['cpu_warning'],
                    recommendation="Monitor CPU usage and consider optimization"
                ))
            
            # Memory usage
            if snapshot.memory_percent > self.thresholds['memory_critical']:
                alerts.append(PerformanceAlert(
                    timestamp=snapshot.timestamp,
                    severity='critical',
                    category='memory',
                    message=f"Critical memory usage: {snapshot.memory_percent:.1f}%",
                    metric_value=snapshot.memory_percent,
                    threshold=self.thresholds['memory_critical'],
                    recommendation="Reduce cache size or clear unused data"
                ))
            elif snapshot.memory_percent > self.thresholds['memory_warning']:
                alerts.append(PerformanceAlert(
                    timestamp=snapshot.timestamp,
                    severity='warning',
                    category='memory',
                    message=f"High memory usage: {snapshot.memory_percent:.1f}%",
                    metric_value=snapshot.memory_percent,
                    threshold=self.thresholds['memory_warning'],
                    recommendation="Monitor memory usage and consider cleanup"
                ))
            
            # Response time
            if snapshot.ui_response_time > self.thresholds['response_time_critical']:
                alerts.append(PerformanceAlert(
                    timestamp=snapshot.timestamp,
                    severity='critical',
                    category='performance',
                    message=f"Critical UI response time: {snapshot.ui_response_time:.2f}s",
                    metric_value=snapshot.ui_response_time,
                    threshold=self.thresholds['response_time_critical'],
                    recommendation="Optimize UI operations and reduce data processing"
                ))
            
            # Store alerts
            with self._lock:
                self.alerts.extend(alerts)
                
                # Keep only recent alerts
                if len(self.alerts) > self.max_alerts:
                    self.alerts = self.alerts[-self.max_alerts:]
            
            # Log critical alerts
            for alert in alerts:
                if alert.severity == 'critical':
                    logger.critical(alert.message)
                elif alert.severity == 'warning':
                    logger.warning(alert.message)
                    
        except Exception as e:
            logger.error(f"Error checking performance thresholds: {e}")
    
    def track_operation(self, operation: str, execution_time: float):
        """Track operation execution time."""
        try:
            with self._lock:
                if operation not in self._operation_times:
                    self._operation_times[operation] = []
                
                self._operation_times[operation].append(execution_time)
                
                # Keep only last 100 measurements
                if len(self._operation_times[operation]) > 100:
                    self._operation_times[operation] = self._operation_times[operation][-100:]
                    
        except Exception as e:
            logger.error(f"Error tracking operation: {e}")
    
    def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data."""
        try:
            with self._lock:
                if not self.snapshots:
                    return {}
                
                latest = self.snapshots[-1]
                
                # Calculate trends (last hour)
                hour_ago = datetime.now() - timedelta(hours=1)
                recent_snapshots = [s for s in self.snapshots if s.timestamp >= hour_ago]
                
                dashboard = {
                    'current_status': {
                        'timestamp': latest.timestamp.isoformat(),
                        'cpu_percent': latest.cpu_percent,
                        'memory_percent': latest.memory_percent,
                        'active_threads': latest.active_threads,
                        'dataframe_count': latest.dataframe_count,
                        'dataframe_memory_mb': latest.dataframe_memory_mb,
                        'parallel_tasks_active': latest.parallel_tasks_active
                    },
                    'performance_metrics': {
                        'avg_indicator_calc_time': latest.avg_indicator_calc_time,
                        'avg_data_load_time': latest.avg_data_load_time,
                        'avg_compression_ratio': latest.avg_compression_ratio,
                        'ui_response_time': latest.ui_response_time,
                        'chart_render_time': latest.chart_render_time
                    },
                    'recent_alerts': [
                        {
                            'timestamp': alert.timestamp.isoformat(),
                            'severity': alert.severity,
                            'category': alert.category,
                            'message': alert.message,
                            'recommendation': alert.recommendation
                        }
                        for alert in self.alerts[-10:]  # Last 10 alerts
                    ],
                    'trends': {
                        'cpu_trend': self._calculate_trend([s.cpu_percent for s in recent_snapshots]),
                        'memory_trend': self._calculate_trend([s.memory_percent for s in recent_snapshots]),
                        'response_time_trend': self._calculate_trend([s.ui_response_time for s in recent_snapshots])
                    },
                    'recommendations': [
                        rec.__dict__ for rec in self.get_optimization_recommendations()[:5]
                    ]
                }
                
                return dashboard
                
        except Exception as e:
            logger.error(f"Error getting performance dashboard: {e}")
            return {}
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction."""
        try:
            if len(values) < 2:
                return 'stable'
            
            # Simple trend calculation
            first_half = values[:len(values)//2]
            second_half = values[len(values)//2:]
            
            if not first_half or not second_half:
                return 'stable'
            
            avg_first = sum(first_half) / len(first_half)
            avg_second = sum(second_half) / len(second_half)
            
            change_percent = ((avg_second - avg_first) / avg_first) * 100 if avg_first > 0 else 0
            
            if change_percent > 10:
                return 'increasing'
            elif change_percent < -10:
                return 'decreasing'
            else:
                return 'stable'
                
        except:
            return 'stable'
    
    def get_optimization_recommendations(self) -> List[OptimizationRecommendation]:
        """Get performance optimization recommendations."""
        try:
            recommendations = []
            
            if not self.snapshots:
                return recommendations
            
            latest = self.snapshots[-1]
            
            # Memory optimization
            if latest.memory_percent > 70:
                recommendations.append(OptimizationRecommendation(
                    category='memory',
                    priority='high',
                    title='Optimize Memory Usage',
                    description=f'Memory usage at {latest.memory_percent:.1f}%. Consider optimization.',
                    potential_improvement='20-30% memory reduction',
                    implementation_effort='medium',
                    action_items=[
                        'Enable data type optimization',
                        'Reduce cache size',
                        'Clear unused DataFrames',
                        'Use memory profiler recommendations'
                    ]
                ))
            
            # CPU optimization
            if latest.cpu_percent > 60:
                recommendations.append(OptimizationRecommendation(
                    category='cpu',
                    priority='medium',
                    title='Optimize CPU Usage',
                    description=f'CPU usage at {latest.cpu_percent:.1f}%. Consider parallel processing optimization.',
                    potential_improvement='15-25% CPU reduction',
                    implementation_effort='low',
                    action_items=[
                        'Adjust parallel processing workers',
                        'Enable vectorized operations',
                        'Optimize indicator calculations'
                    ]
                ))
            
            # Compression optimization
            if latest.avg_compression_ratio < 2.0:
                recommendations.append(OptimizationRecommendation(
                    category='storage',
                    priority='low',
                    title='Improve Compression Efficiency',
                    description=f'Compression ratio at {latest.avg_compression_ratio:.1f}x. Better algorithms available.',
                    potential_improvement='30-50% storage reduction',
                    implementation_effort='low',
                    action_items=[
                        'Switch to better compression algorithm',
                        'Enable data type optimization',
                        'Use compression benchmarking'
                    ]
                ))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {e}")
            return []
    
    def export_performance_report(self, filepath: Optional[Path] = None) -> bool:
        """Export comprehensive performance report."""
        try:
            if filepath is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filepath = Path(f"performance_report_{timestamp}.json")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'dashboard': self.get_performance_dashboard(),
                'memory_report': self.memory_profiler.get_current_memory_usage(),
                'compression_benchmarks': {
                    name: asdict(benchmark) 
                    for name, benchmark in self.compression_manager.get_compression_benchmarks().items()
                },
                'parallel_metrics': asdict(self.parallel_processor.get_performance_metrics()),
                'configuration': {
                    'monitoring_interval': self.monitoring_interval,
                    'thresholds': self.thresholds,
                    'max_snapshots': self.max_snapshots
                }
            }
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Performance report exported to: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting performance report: {e}")
            return False
