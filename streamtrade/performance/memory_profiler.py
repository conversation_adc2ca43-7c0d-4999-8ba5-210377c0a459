"""
Advanced Memory Profiler for Lionaire platform.
Phase 4.1.3 - Advanced Memory Profiling.

Features:
- Real-time memory usage tracking
- Memory leak detection
- Peak memory usage analysis
- Automatic optimization recommendations
- Memory pressure detection
"""

import psutil
import gc
import threading
import time
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
import json

from ..config.logging_config import get_logger
from ..core.utils import estimate_memory_usage

logger = get_logger(__name__)


@dataclass
class MemorySnapshot:
    """Memory usage snapshot."""
    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    memory_percent: float
    process_memory_mb: float
    process_memory_percent: float
    gc_objects: int
    dataframe_count: int
    dataframe_memory_mb: float


@dataclass
class MemoryLeak:
    """Memory leak detection result."""
    detected: bool
    growth_rate_mb_per_hour: float
    confidence: float
    duration_hours: float
    start_memory_mb: float
    end_memory_mb: float
    recommendation: str


@dataclass
class MemoryOptimization:
    """Memory optimization recommendation."""
    action: str
    description: str
    potential_savings_mb: float
    priority: str  # 'high', 'medium', 'low'
    implementation: str


class MemoryProfiler:
    """
    Advanced memory profiler with leak detection and optimization.
    """
    
    def __init__(self, monitoring_interval: int = 30):
        """
        Initialize Memory Profiler.
        
        Args:
            monitoring_interval: Monitoring interval in seconds
        """
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.snapshots: List[MemorySnapshot] = []
        self.max_snapshots = 1000  # Keep last 1000 snapshots
        
        # Thread safety
        self._lock = threading.RLock()
        self._monitor_thread: Optional[threading.Thread] = None
        
        # Memory thresholds
        self.warning_threshold = 80  # % memory usage
        self.critical_threshold = 90  # % memory usage
        
        # Leak detection parameters
        self.leak_detection_window = 24  # hours
        self.leak_threshold_mb_per_hour = 10  # MB/hour growth
        
        # Process reference
        self.process = psutil.Process()
        
        logger.info(f"Memory Profiler initialized (interval: {monitoring_interval}s)")
    
    def start_monitoring(self):
        """Start continuous memory monitoring."""
        try:
            if self.is_monitoring:
                logger.warning("Memory monitoring already running")
                return
            
            self.is_monitoring = True
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            
            logger.info("Memory monitoring started")
            
        except Exception as e:
            logger.error(f"Error starting memory monitoring: {e}")
            self.is_monitoring = False
    
    def stop_monitoring(self):
        """Stop memory monitoring."""
        try:
            self.is_monitoring = False
            
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            
            logger.info("Memory monitoring stopped")
            
        except Exception as e:
            logger.error(f"Error stopping memory monitoring: {e}")
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                snapshot = self._take_snapshot()
                
                with self._lock:
                    self.snapshots.append(snapshot)
                    
                    # Keep only recent snapshots
                    if len(self.snapshots) > self.max_snapshots:
                        self.snapshots = self.snapshots[-self.max_snapshots:]
                
                # Check for memory pressure
                self._check_memory_pressure(snapshot)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _take_snapshot(self) -> MemorySnapshot:
        """Take a memory usage snapshot."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process_memory = self.process.memory_info()
            process_memory_mb = process_memory.rss / 1024 / 1024
            process_memory_percent = self.process.memory_percent()
            
            # Garbage collection info
            gc_objects = len(gc.get_objects())
            
            # DataFrame analysis
            dataframe_count, dataframe_memory_mb = self._analyze_dataframes()
            
            snapshot = MemorySnapshot(
                timestamp=datetime.now(),
                total_memory_mb=memory.total / 1024 / 1024,
                available_memory_mb=memory.available / 1024 / 1024,
                used_memory_mb=memory.used / 1024 / 1024,
                memory_percent=memory.percent,
                process_memory_mb=process_memory_mb,
                process_memory_percent=process_memory_percent,
                gc_objects=gc_objects,
                dataframe_count=dataframe_count,
                dataframe_memory_mb=dataframe_memory_mb
            )
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error taking memory snapshot: {e}")
            # Return empty snapshot
            return MemorySnapshot(
                timestamp=datetime.now(),
                total_memory_mb=0, available_memory_mb=0, used_memory_mb=0,
                memory_percent=0, process_memory_mb=0, process_memory_percent=0,
                gc_objects=0, dataframe_count=0, dataframe_memory_mb=0
            )
    
    def _analyze_dataframes(self) -> Tuple[int, float]:
        """Analyze DataFrame memory usage."""
        try:
            dataframe_count = 0
            total_memory_mb = 0
            
            # Get all objects
            for obj in gc.get_objects():
                if isinstance(obj, pd.DataFrame):
                    dataframe_count += 1
                    try:
                        memory_info = estimate_memory_usage(obj)
                        total_memory_mb += memory_info.get('total_mb', 0)
                    except:
                        pass  # Skip problematic DataFrames
            
            return dataframe_count, total_memory_mb
            
        except Exception as e:
            logger.error(f"Error analyzing DataFrames: {e}")
            return 0, 0.0
    
    def _check_memory_pressure(self, snapshot: MemorySnapshot):
        """Check for memory pressure and take action."""
        try:
            if snapshot.memory_percent > self.critical_threshold:
                logger.critical(f"Critical memory usage: {snapshot.memory_percent:.1f}%")
                self._trigger_emergency_cleanup()
                
            elif snapshot.memory_percent > self.warning_threshold:
                logger.warning(f"High memory usage: {snapshot.memory_percent:.1f}%")
                self._suggest_cleanup()
                
        except Exception as e:
            logger.error(f"Error checking memory pressure: {e}")
    
    def _trigger_emergency_cleanup(self):
        """Trigger emergency memory cleanup."""
        try:
            logger.info("Triggering emergency memory cleanup")
            
            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
            
            # Additional cleanup could be implemented here
            # e.g., clearing caches, reducing cache sizes, etc.
            
        except Exception as e:
            logger.error(f"Error in emergency cleanup: {e}")
    
    def _suggest_cleanup(self):
        """Suggest memory cleanup actions."""
        try:
            optimizations = self.get_optimization_recommendations()
            
            if optimizations:
                logger.info("Memory optimization recommendations:")
                for opt in optimizations[:3]:  # Top 3 recommendations
                    logger.info(f"  - {opt.action}: {opt.description}")
                    
        except Exception as e:
            logger.error(f"Error suggesting cleanup: {e}")
    
    def get_current_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage information."""
        try:
            snapshot = self._take_snapshot()
            
            return {
                'system_memory': {
                    'total_gb': round(snapshot.total_memory_mb / 1024, 2),
                    'used_gb': round(snapshot.used_memory_mb / 1024, 2),
                    'available_gb': round(snapshot.available_memory_mb / 1024, 2),
                    'usage_percent': round(snapshot.memory_percent, 1)
                },
                'process_memory': {
                    'used_mb': round(snapshot.process_memory_mb, 1),
                    'usage_percent': round(snapshot.process_memory_percent, 1)
                },
                'dataframes': {
                    'count': snapshot.dataframe_count,
                    'memory_mb': round(snapshot.dataframe_memory_mb, 1)
                },
                'gc_objects': snapshot.gc_objects,
                'timestamp': snapshot.timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {}
    
    def detect_memory_leaks(self) -> MemoryLeak:
        """Detect potential memory leaks."""
        try:
            with self._lock:
                if len(self.snapshots) < 10:
                    return MemoryLeak(
                        detected=False,
                        growth_rate_mb_per_hour=0,
                        confidence=0,
                        duration_hours=0,
                        start_memory_mb=0,
                        end_memory_mb=0,
                        recommendation="Insufficient data for leak detection"
                    )
                
                # Analyze memory growth over time
                recent_snapshots = self.snapshots[-100:]  # Last 100 snapshots
                
                # Calculate time span
                start_time = recent_snapshots[0].timestamp
                end_time = recent_snapshots[-1].timestamp
                duration_hours = (end_time - start_time).total_seconds() / 3600
                
                if duration_hours < 1:
                    return MemoryLeak(
                        detected=False,
                        growth_rate_mb_per_hour=0,
                        confidence=0,
                        duration_hours=duration_hours,
                        start_memory_mb=0,
                        end_memory_mb=0,
                        recommendation="Monitoring duration too short"
                    )
                
                # Calculate memory growth
                start_memory = recent_snapshots[0].process_memory_mb
                end_memory = recent_snapshots[-1].process_memory_mb
                growth_mb = end_memory - start_memory
                growth_rate_mb_per_hour = growth_mb / duration_hours
                
                # Calculate confidence based on consistency of growth
                memory_values = [s.process_memory_mb for s in recent_snapshots]
                correlation = np.corrcoef(range(len(memory_values)), memory_values)[0, 1]
                confidence = abs(correlation) * 100  # Convert to percentage
                
                # Determine if leak is detected
                detected = (
                    growth_rate_mb_per_hour > self.leak_threshold_mb_per_hour and
                    confidence > 70  # High confidence in growth trend
                )
                
                # Generate recommendation
                if detected:
                    if growth_rate_mb_per_hour > 50:
                        recommendation = "Critical memory leak detected. Immediate investigation required."
                    elif growth_rate_mb_per_hour > 20:
                        recommendation = "Significant memory leak detected. Investigation recommended."
                    else:
                        recommendation = "Potential memory leak detected. Monitor closely."
                else:
                    recommendation = "No significant memory leak detected."
                
                return MemoryLeak(
                    detected=detected,
                    growth_rate_mb_per_hour=growth_rate_mb_per_hour,
                    confidence=confidence,
                    duration_hours=duration_hours,
                    start_memory_mb=start_memory,
                    end_memory_mb=end_memory,
                    recommendation=recommendation
                )
                
        except Exception as e:
            logger.error(f"Error detecting memory leaks: {e}")
            return MemoryLeak(
                detected=False,
                growth_rate_mb_per_hour=0,
                confidence=0,
                duration_hours=0,
                start_memory_mb=0,
                end_memory_mb=0,
                recommendation=f"Error in leak detection: {e}"
            )
    
    def get_optimization_recommendations(self) -> List[MemoryOptimization]:
        """Get memory optimization recommendations."""
        try:
            recommendations = []
            current_usage = self.get_current_memory_usage()
            
            # Check DataFrame memory usage
            df_memory = current_usage.get('dataframes', {}).get('memory_mb', 0)
            if df_memory > 500:  # > 500MB in DataFrames
                recommendations.append(MemoryOptimization(
                    action="Optimize DataFrame Storage",
                    description=f"DataFrames using {df_memory:.1f}MB. Consider data type optimization.",
                    potential_savings_mb=df_memory * 0.3,  # Estimate 30% savings
                    priority="high",
                    implementation="Use float32 instead of float64 where appropriate"
                ))
            
            # Check garbage collection
            gc_objects = current_usage.get('gc_objects', 0)
            if gc_objects > 100000:  # > 100k objects
                recommendations.append(MemoryOptimization(
                    action="Garbage Collection",
                    description=f"{gc_objects:,} objects in memory. Force garbage collection.",
                    potential_savings_mb=50,  # Estimate
                    priority="medium",
                    implementation="Call gc.collect() to free unused objects"
                ))
            
            # Check system memory pressure
            memory_percent = current_usage.get('system_memory', {}).get('usage_percent', 0)
            if memory_percent > 80:
                recommendations.append(MemoryOptimization(
                    action="Reduce Cache Size",
                    description=f"System memory at {memory_percent}%. Reduce cache sizes.",
                    potential_savings_mb=200,  # Estimate
                    priority="high",
                    implementation="Reduce max_cache_size_gb setting"
                ))
            
            # Sort by priority
            priority_order = {'high': 0, 'medium': 1, 'low': 2}
            recommendations.sort(key=lambda x: priority_order.get(x.priority, 3))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {e}")
            return []
    
    def get_memory_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get memory usage history."""
        try:
            with self._lock:
                cutoff_time = datetime.now() - timedelta(hours=hours)
                recent_snapshots = [
                    s for s in self.snapshots 
                    if s.timestamp >= cutoff_time
                ]
                
                history = []
                for snapshot in recent_snapshots:
                    history.append({
                        'timestamp': snapshot.timestamp.isoformat(),
                        'memory_percent': snapshot.memory_percent,
                        'process_memory_mb': snapshot.process_memory_mb,
                        'dataframe_memory_mb': snapshot.dataframe_memory_mb,
                        'gc_objects': snapshot.gc_objects
                    })
                
                return history
                
        except Exception as e:
            logger.error(f"Error getting memory history: {e}")
            return []
    
    def export_memory_report(self, filepath: Optional[Path] = None) -> bool:
        """Export detailed memory report."""
        try:
            if filepath is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filepath = Path(f"memory_report_{timestamp}.json")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'current_usage': self.get_current_memory_usage(),
                'leak_detection': self.detect_memory_leaks().__dict__,
                'optimizations': [opt.__dict__ for opt in self.get_optimization_recommendations()],
                'history': self.get_memory_history(24),
                'configuration': {
                    'monitoring_interval': self.monitoring_interval,
                    'warning_threshold': self.warning_threshold,
                    'critical_threshold': self.critical_threshold,
                    'leak_threshold_mb_per_hour': self.leak_threshold_mb_per_hour
                }
            }
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Memory report exported to: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting memory report: {e}")
            return False
