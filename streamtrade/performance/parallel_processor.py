"""
Parallel Processing Manager for Lionaire platform.
Phase 4.1.2 - Parallel Processing Enhancement.

Features:
- Multi-threaded indicator calculations
- Thread pool management
- Load balancing across CPU cores
- Vectorized operations optimization
- Async data loading
"""

import threading
import multiprocessing
import concurrent.futures
import asyncio
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
import queue
import functools

from ..config.logging_config import get_logger
from ..config.user_settings import get_user_settings
from ..indicators.technical_indicators import TechnicalIndicators

logger = get_logger(__name__)


@dataclass
class ProcessingTask:
    """Processing task definition."""
    task_id: str
    function: Callable
    args: tuple
    kwargs: dict
    priority: int = 0  # Higher number = higher priority
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ProcessingResult:
    """Processing result."""
    task_id: str
    success: bool
    result: Any
    error_message: Optional[str]
    execution_time: float
    worker_id: str


@dataclass
class PerformanceMetrics:
    """Performance metrics for parallel processing."""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_execution_time: float
    total_execution_time: float
    throughput_tasks_per_second: float
    cpu_utilization: float
    active_workers: int


class ParallelProcessor:
    """
    Advanced parallel processing manager with load balancing and optimization.
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize Parallel Processor.
        
        Args:
            max_workers: Maximum number of worker threads (default: CPU count)
        """
        self.max_workers = max_workers or min(multiprocessing.cpu_count(), 8)
        self.user_settings = get_user_settings()
        
        # Thread pool
        self.executor: Optional[concurrent.futures.ThreadPoolExecutor] = None
        self.is_running = False
        
        # Task management
        self.task_queue = queue.PriorityQueue()
        self.results: Dict[str, ProcessingResult] = {}
        self.active_tasks: Dict[str, ProcessingTask] = {}
        
        # Performance tracking
        self.metrics = PerformanceMetrics(
            total_tasks=0, completed_tasks=0, failed_tasks=0,
            avg_execution_time=0, total_execution_time=0,
            throughput_tasks_per_second=0, cpu_utilization=0,
            active_workers=0
        )
        
        # Thread safety
        self._lock = threading.RLock()
        self._results_lock = threading.RLock()
        
        # Optimization settings
        self.enable_vectorization = True
        self.chunk_size = 10000  # For chunked processing
        
        logger.info(f"Parallel Processor initialized with {self.max_workers} workers")
    
    def start(self):
        """Start the parallel processor."""
        try:
            if self.is_running:
                logger.warning("Parallel processor already running")
                return
            
            self.executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=self.max_workers,
                thread_name_prefix="LionaireWorker"
            )
            self.is_running = True
            
            logger.info(f"Parallel processor started with {self.max_workers} workers")
            
        except Exception as e:
            logger.error(f"Error starting parallel processor: {e}")
            self.is_running = False
    
    def stop(self):
        """Stop the parallel processor."""
        try:
            if not self.is_running:
                return
            
            self.is_running = False
            
            if self.executor:
                self.executor.shutdown(wait=True, timeout=30)
                self.executor = None
            
            logger.info("Parallel processor stopped")
            
        except Exception as e:
            logger.error(f"Error stopping parallel processor: {e}")
    
    def submit_task(self, task: ProcessingTask) -> bool:
        """
        Submit a task for parallel processing.
        
        Args:
            task: Processing task
            
        Returns:
            True if submitted successfully
        """
        try:
            if not self.is_running:
                self.start()
            
            with self._lock:
                # Add to active tasks
                self.active_tasks[task.task_id] = task
                
                # Submit to executor
                future = self.executor.submit(self._execute_task, task)
                future.add_done_callback(lambda f: self._handle_task_completion(f, task.task_id))
                
                self.metrics.total_tasks += 1
                
            logger.debug(f"Task submitted: {task.task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error submitting task: {e}")
            return False
    
    def _execute_task(self, task: ProcessingTask) -> ProcessingResult:
        """Execute a single task."""
        start_time = time.time()
        worker_id = threading.current_thread().name
        
        try:
            # Execute the function
            result = task.function(*task.args, **task.kwargs)
            execution_time = time.time() - start_time
            
            return ProcessingResult(
                task_id=task.task_id,
                success=True,
                result=result,
                error_message=None,
                execution_time=execution_time,
                worker_id=worker_id
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Task {task.task_id} failed: {e}")
            
            return ProcessingResult(
                task_id=task.task_id,
                success=False,
                result=None,
                error_message=str(e),
                execution_time=execution_time,
                worker_id=worker_id
            )
    
    def _handle_task_completion(self, future: concurrent.futures.Future, task_id: str):
        """Handle task completion."""
        try:
            result = future.result()
            
            with self._results_lock:
                self.results[task_id] = result
                
                # Update metrics
                if result.success:
                    self.metrics.completed_tasks += 1
                else:
                    self.metrics.failed_tasks += 1
                
                self.metrics.total_execution_time += result.execution_time
                self.metrics.avg_execution_time = (
                    self.metrics.total_execution_time / 
                    (self.metrics.completed_tasks + self.metrics.failed_tasks)
                )
            
            with self._lock:
                # Remove from active tasks
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
            
            logger.debug(f"Task completed: {task_id} (success: {result.success})")
            
        except Exception as e:
            logger.error(f"Error handling task completion: {e}")
    
    def calculate_indicators_parallel(self, data: pd.DataFrame, indicators: Dict[str, Dict]) -> Dict[str, Any]:
        """
        Calculate multiple indicators in parallel.
        
        Args:
            data: OHLCV DataFrame
            indicators: Dictionary of indicator configurations
            
        Returns:
            Dictionary of indicator results
        """
        try:
            if not indicators:
                return {}
            
            # Group indicators by independence
            independent_indicators = []
            dependent_indicators = []
            
            for name, config in indicators.items():
                indicator_type = config.get('indicator_type', '')
                
                # Check if indicator depends on others
                if self._is_independent_indicator(indicator_type):
                    independent_indicators.append((name, config))
                else:
                    dependent_indicators.append((name, config))
            
            results = {}
            
            # Process independent indicators in parallel
            if independent_indicators:
                parallel_results = self._process_indicators_parallel(data, independent_indicators)
                results.update(parallel_results)
            
            # Process dependent indicators sequentially (for now)
            for name, config in dependent_indicators:
                try:
                    indicator_result = self._calculate_single_indicator(data, config)
                    if indicator_result:
                        results[name] = indicator_result
                except Exception as e:
                    logger.error(f"Error calculating dependent indicator {name}: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in parallel indicator calculation: {e}")
            return {}
    
    def _process_indicators_parallel(self, data: pd.DataFrame, indicators: List[Tuple[str, Dict]]) -> Dict[str, Any]:
        """Process independent indicators in parallel."""
        try:
            # Submit tasks for each indicator
            task_futures = {}
            
            for name, config in indicators:
                task_id = f"indicator_{name}_{int(time.time() * 1000)}"
                
                task = ProcessingTask(
                    task_id=task_id,
                    function=self._calculate_single_indicator,
                    args=(data, config),
                    kwargs={},
                    priority=1
                )
                
                if self.submit_task(task):
                    task_futures[name] = task_id
            
            # Wait for results
            results = {}
            timeout = 30  # 30 seconds timeout
            start_time = time.time()
            
            while task_futures and (time.time() - start_time) < timeout:
                completed_tasks = []
                
                for name, task_id in task_futures.items():
                    if task_id in self.results:
                        result = self.results[task_id]
                        if result.success:
                            results[name] = result.result
                        else:
                            logger.error(f"Indicator {name} calculation failed: {result.error_message}")
                        completed_tasks.append(name)
                
                # Remove completed tasks
                for name in completed_tasks:
                    del task_futures[name]
                
                if task_futures:
                    time.sleep(0.1)  # Small delay
            
            # Handle timeout
            if task_futures:
                logger.warning(f"Timeout waiting for indicators: {list(task_futures.keys())}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing indicators in parallel: {e}")
            return {}
    
    def _calculate_single_indicator(self, data: pd.DataFrame, config: Dict) -> Optional[Any]:
        """Calculate a single indicator."""
        try:
            indicator_type = config.get('indicator_type', '')
            parameters = config.get('parameters', {})
            
            # Get the indicator function
            if hasattr(TechnicalIndicators, indicator_type):
                indicator_func = getattr(TechnicalIndicators, indicator_type)
                return indicator_func(data, **parameters)
            else:
                logger.error(f"Unknown indicator type: {indicator_type}")
                return None
                
        except Exception as e:
            logger.error(f"Error calculating indicator {indicator_type}: {e}")
            return None
    
    def _is_independent_indicator(self, indicator_type: str) -> bool:
        """Check if indicator can be calculated independently."""
        # Most technical indicators are independent
        # Only complex indicators like Ichimoku or custom composite indicators might be dependent
        independent_types = {
            'sma', 'ema', 'wma', 'rsi', 'macd', 'bollinger_bands',
            'stochastic', 'atr', 'adx', 'cci', 'williams_r',
            'momentum', 'roc', 'trix', 'kalman_filter'
        }
        
        return indicator_type.lower() in independent_types
    
    def process_data_chunks(self, data: pd.DataFrame, processing_func: Callable, chunk_size: Optional[int] = None) -> pd.DataFrame:
        """
        Process large DataFrame in parallel chunks.
        
        Args:
            data: Large DataFrame to process
            processing_func: Function to apply to each chunk
            chunk_size: Size of each chunk
            
        Returns:
            Processed DataFrame
        """
        try:
            if chunk_size is None:
                chunk_size = self.chunk_size
            
            if len(data) <= chunk_size:
                # Data is small enough, process directly
                return processing_func(data)
            
            # Split data into chunks
            chunks = [data.iloc[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
            
            # Process chunks in parallel
            task_futures = {}
            
            for i, chunk in enumerate(chunks):
                task_id = f"chunk_{i}_{int(time.time() * 1000)}"
                
                task = ProcessingTask(
                    task_id=task_id,
                    function=processing_func,
                    args=(chunk,),
                    kwargs={},
                    priority=1
                )
                
                if self.submit_task(task):
                    task_futures[i] = task_id
            
            # Collect results
            processed_chunks = {}
            timeout = 60  # 60 seconds timeout
            start_time = time.time()
            
            while task_futures and (time.time() - start_time) < timeout:
                completed_tasks = []
                
                for chunk_idx, task_id in task_futures.items():
                    if task_id in self.results:
                        result = self.results[task_id]
                        if result.success:
                            processed_chunks[chunk_idx] = result.result
                        completed_tasks.append(chunk_idx)
                
                for chunk_idx in completed_tasks:
                    del task_futures[chunk_idx]
                
                if task_futures:
                    time.sleep(0.1)
            
            # Combine results
            if len(processed_chunks) == len(chunks):
                # All chunks processed successfully
                combined_data = pd.concat([processed_chunks[i] for i in range(len(chunks))], ignore_index=True)
                return combined_data
            else:
                logger.warning("Some chunks failed to process, falling back to sequential processing")
                return processing_func(data)
                
        except Exception as e:
            logger.error(f"Error in parallel chunk processing: {e}")
            return processing_func(data)  # Fallback to sequential
    
    def optimize_dataframe_operations(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame operations using vectorization.
        
        Args:
            data: DataFrame to optimize
            
        Returns:
            Optimized DataFrame
        """
        try:
            if not self.enable_vectorization:
                return data
            
            optimized_data = data.copy()
            
            # Vectorized operations for common calculations
            if all(col in optimized_data.columns for col in ['high', 'low']):
                # Calculate typical price vectorized
                optimized_data['typical_price'] = (
                    optimized_data['high'] + optimized_data['low'] + optimized_data['close']
                ) / 3
            
            if all(col in optimized_data.columns for col in ['open', 'close']):
                # Calculate price change vectorized
                optimized_data['price_change'] = optimized_data['close'] - optimized_data['open']
                optimized_data['price_change_pct'] = (
                    optimized_data['price_change'] / optimized_data['open'] * 100
                )
            
            # Use NumPy for faster calculations where possible
            if 'close' in optimized_data.columns:
                # Vectorized returns calculation
                optimized_data['returns'] = np.log(optimized_data['close'] / optimized_data['close'].shift(1))
            
            return optimized_data
            
        except Exception as e:
            logger.error(f"Error optimizing DataFrame operations: {e}")
            return data
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        try:
            with self._lock:
                # Update active workers count
                self.metrics.active_workers = len(self.active_tasks)
                
                # Calculate throughput
                if self.metrics.total_execution_time > 0:
                    self.metrics.throughput_tasks_per_second = (
                        self.metrics.completed_tasks / self.metrics.total_execution_time
                    )
                
                return self.metrics
                
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return self.metrics
    
    def clear_completed_results(self):
        """Clear completed task results to free memory."""
        try:
            with self._results_lock:
                self.results.clear()
            
            logger.debug("Cleared completed task results")
            
        except Exception as e:
            logger.error(f"Error clearing results: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
