# Core Dependencies (Latest Compatible Versions)
streamlit>=1.39.0
plotly>=5.24.1
pandas>=2.2.3
numpy>=1.26.4

# Technical Analysis (Compatible with numpy 1.x)
# pandas-ta>=0.3.14b0  # Temporarily disabled due to numpy 2+ compatibility issues
# ta-lib>=0.4.32       # Optional, using custom implementations

# Date/Time Handling
python-dateutil>=2.9.0
pytz>=2024.2

# Performance & Optimization
numba>=0.60.0
joblib>=1.4.2
psutil>=6.1.0  # For memory and CPU monitoring

# Data Handling
openpyxl>=3.1.5
xlsxwriter>=3.2.0

# Phase 4 - Performance & Export Dependencies
lz4>=4.3.3  # Fast compression
brotli>=1.1.0  # High compression ratio
snappy-python>=0.7.3  # Balanced compression (optional)
kaleido>=0.2.1  # For PDF/PNG chart export

# Smart Disk Cache System (Phase 5.4)
pyarrow>=18.0.0  # For Parquet file support
fastparquet>=2024.11.0  # Alternative Parquet engine

# Logging & Configuration
pyyaml>=6.0.2
colorlog>=6.8.2

# Development & Testing
pytest>=8.3.3
pytest-cov>=5.0.0
black>=24.10.0
flake8>=7.1.1

# Additional Dependencies for Better Compatibility
scipy>=1.14.1
scikit-learn>=1.5.2

# Optional: For future live trading
# websocket-client>=1.8.0
# requests>=2.32.3
