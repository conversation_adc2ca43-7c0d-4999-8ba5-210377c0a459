#!/usr/bin/env python3
"""
Simple Phase 4 Test - Direct testing without complex imports
"""

import sys
import os
import tempfile
import shutil
import time
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from pathlib import Path
from datetime import datetime

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_compression_basic():
    """Test basic compression functionality."""
    print("🧪 Testing Basic Compression...")
    
    try:
        import lz4.frame
        import gzip
        import brotli
        
        # Create test data
        test_data = b"Hello World! " * 1000
        
        # Test LZ4
        lz4_compressed = lz4.frame.compress(test_data)
        lz4_decompressed = lz4.frame.decompress(lz4_compressed)
        lz4_ratio = len(test_data) / len(lz4_compressed)
        
        # Test Gzip
        gzip_compressed = gzip.compress(test_data)
        gzip_decompressed = gzip.decompress(gzip_compressed)
        gzip_ratio = len(test_data) / len(gzip_compressed)
        
        # Test Brotli
        brotli_compressed = brotli.compress(test_data)
        brotli_decompressed = brotli.decompress(brotli_compressed)
        brotli_ratio = len(test_data) / len(brotli_compressed)
        
        print(f"  ✅ LZ4: {lz4_ratio:.2f}x compression")
        print(f"  ✅ Gzip: {gzip_ratio:.2f}x compression")
        print(f"  ✅ Brotli: {brotli_ratio:.2f}x compression")
        
        # Verify decompression
        assert lz4_decompressed == test_data
        assert gzip_decompressed == test_data
        assert brotli_decompressed == test_data
        
        print("  ✅ All compression tests passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Compression test failed: {e}")
        return False

def test_memory_monitoring():
    """Test basic memory monitoring."""
    print("\n🧪 Testing Memory Monitoring...")
    
    try:
        import psutil
        
        # Get current memory usage
        memory = psutil.virtual_memory()
        process = psutil.Process()
        process_memory = process.memory_info()
        
        print(f"  ✅ System memory: {memory.percent:.1f}% used")
        print(f"  ✅ Process memory: {process_memory.rss / 1024 / 1024:.1f} MB")
        print(f"  ✅ Available memory: {memory.available / 1024 / 1024 / 1024:.1f} GB")
        
        # Test memory allocation
        large_data = np.random.random((1000, 1000))
        new_memory = psutil.virtual_memory()
        
        print(f"  ✅ Memory after allocation: {new_memory.percent:.1f}% used")
        print("  ✅ Memory monitoring tests passed")
        
        del large_data  # Clean up
        return True
        
    except Exception as e:
        print(f"  ❌ Memory monitoring test failed: {e}")
        return False

def test_parallel_processing():
    """Test basic parallel processing."""
    print("\n🧪 Testing Parallel Processing...")
    
    try:
        import concurrent.futures
        import threading
        
        def test_function(x):
            time.sleep(0.1)  # Simulate work
            return x * x
        
        # Test sequential
        start_time = time.time()
        sequential_results = [test_function(i) for i in range(5)]
        sequential_time = time.time() - start_time
        
        # Test parallel
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            parallel_results = list(executor.map(test_function, range(5)))
        parallel_time = time.time() - start_time
        
        speedup = sequential_time / parallel_time
        
        print(f"  ✅ Sequential time: {sequential_time:.2f}s")
        print(f"  ✅ Parallel time: {parallel_time:.2f}s")
        print(f"  ✅ Speedup: {speedup:.2f}x")
        
        # Verify results
        assert sequential_results == parallel_results
        assert speedup > 1.0  # Should be faster
        
        print("  ✅ Parallel processing tests passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Parallel processing test failed: {e}")
        return False

def test_chart_export():
    """Test basic chart export."""
    print("\n🧪 Testing Chart Export...")
    
    try:
        import plotly.io as pio
        
        # Create test data
        dates = pd.date_range('2024-01-01', periods=50, freq='1H')
        test_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 50),
            'high': np.random.uniform(1.1, 2.1, 50),
            'low': np.random.uniform(0.9, 1.9, 50),
            'close': np.random.uniform(1.0, 2.0, 50),
        }, index=dates)
        
        # Create test chart
        fig = go.Figure()
        fig.add_trace(go.Candlestick(
            x=test_data.index,
            open=test_data['open'],
            high=test_data['high'],
            low=test_data['low'],
            close=test_data['close'],
            name='Test Data'
        ))
        
        # Test directory
        test_dir = Path(tempfile.mkdtemp())
        
        try:
            # Test PNG export
            png_path = test_dir / 'test_chart.png'
            pio.write_image(fig, png_path, format='png', width=800, height=600)
            png_size = png_path.stat().st_size / 1024  # KB
            
            # Test HTML export
            html_path = test_dir / 'test_chart.html'
            html_content = pio.to_html(fig, include_plotlyjs=True)
            with open(html_path, 'w') as f:
                f.write(html_content)
            html_size = html_path.stat().st_size / 1024  # KB
            
            print(f"  ✅ PNG export: {png_size:.1f} KB")
            print(f"  ✅ HTML export: {html_size:.1f} KB")
            print("  ✅ Chart export tests passed")
            
            return True
            
        finally:
            # Clean up
            shutil.rmtree(test_dir)
        
    except Exception as e:
        print(f"  ❌ Chart export test failed: {e}")
        return False

def test_data_export():
    """Test basic data export."""
    print("\n🧪 Testing Data Export...")
    
    try:
        # Create test data
        dates = pd.date_range('2024-01-01', periods=100, freq='1H')
        test_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # Test directory
        test_dir = Path(tempfile.mkdtemp())
        
        try:
            # Test CSV export
            csv_path = test_dir / 'test_data.csv'
            test_data.to_csv(csv_path)
            csv_size = csv_path.stat().st_size / 1024  # KB
            
            # Test JSON export
            json_path = test_dir / 'test_data.json'
            test_data.to_json(json_path, orient='records', date_format='iso')
            json_size = json_path.stat().st_size / 1024  # KB
            
            # Test Parquet export
            parquet_path = test_dir / 'test_data.parquet'
            test_data.to_parquet(parquet_path)
            parquet_size = parquet_path.stat().st_size / 1024  # KB
            
            print(f"  ✅ CSV export: {csv_size:.1f} KB")
            print(f"  ✅ JSON export: {json_size:.1f} KB")
            print(f"  ✅ Parquet export: {parquet_size:.1f} KB")
            print("  ✅ Data export tests passed")
            
            return True
            
        finally:
            # Clean up
            shutil.rmtree(test_dir)
        
    except Exception as e:
        print(f"  ❌ Data export test failed: {e}")
        return False

def test_configuration_management():
    """Test basic configuration management."""
    print("\n🧪 Testing Configuration Management...")
    
    try:
        import json
        
        # Test configuration
        test_config = {
            'workspace': {
                'name': 'Test Workspace',
                'pair': 'EURUSD',
                'timeframe': 'H1',
                'indicators': {
                    'SMA_20': {'type': 'sma', 'period': 20},
                    'RSI': {'type': 'rsi', 'period': 14}
                }
            },
            'settings': {
                'theme': 'dark',
                'chart_height': 600,
                'decimal_places': 4
            }
        }
        
        # Test directory
        test_dir = Path(tempfile.mkdtemp())
        
        try:
            # Test JSON save/load
            config_path = test_dir / 'test_config.json'
            with open(config_path, 'w') as f:
                json.dump(test_config, f, indent=2)
            
            with open(config_path, 'r') as f:
                loaded_config = json.load(f)
            
            # Verify configuration
            assert loaded_config == test_config
            
            config_size = config_path.stat().st_size / 1024  # KB
            
            print(f"  ✅ Configuration saved: {config_size:.1f} KB")
            print(f"  ✅ Configuration loaded successfully")
            print(f"  ✅ Workspace: {loaded_config['workspace']['name']}")
            print(f"  ✅ Indicators: {len(loaded_config['workspace']['indicators'])}")
            print("  ✅ Configuration management tests passed")
            
            return True
            
        finally:
            # Clean up
            shutil.rmtree(test_dir)
        
    except Exception as e:
        print(f"  ❌ Configuration management test failed: {e}")
        return False

def main():
    """Run all Phase 4 tests."""
    print("🚀 Lionaire Platform - Phase 4 Simple Tests")
    print("=" * 60)
    
    tests = [
        test_compression_basic,
        test_memory_monitoring,
        test_parallel_processing,
        test_chart_export,
        test_data_export,
        test_configuration_management
    ]
    
    results = []
    
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"  💥 Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 Phase 4 Simple Test Summary")
    
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🏆 All Phase 4 core features working!")
        print("✅ Ready for full implementation testing")
    elif success_rate >= 80:
        print("⚠️  Most core features working")
        print("🔧 Some features may need attention")
    else:
        print("❌ Multiple core features failing")
        print("🚨 Requires investigation")
    
    return success_rate == 100

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
