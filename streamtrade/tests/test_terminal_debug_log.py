#!/usr/bin/env python3
"""
Test script to verify terminal debug logging for indicator cache.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.visualization.chart_viewer import <PERSON><PERSON>iewer
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_terminal_debug_logging():
    """Test terminal debug logging for indicator operations."""
    print("🧪 Testing Terminal Debug Logging for Indicator Cache...")
    
    try:
        # Initialize chart viewer
        chart_viewer = ChartViewer()
        
        # Load data
        print("\n📊 Loading data...")
        success = chart_viewer.load_data_n_days_back("EURUSD", "H1", 3)
        if not success:
            print("❌ Failed to load data")
            return False
        
        print(f"✅ Loaded {len(chart_viewer.current_data)} candles")
        
        # Add indicators to trigger terminal debug logging
        print("\n📈 Adding indicators to trigger debug logging...")
        
        # Add SMA indicator
        print("\n1. Adding SMA indicator...")
        sma_success = chart_viewer.add_indicator("SMA_20", "SMA", {"period": 20})
        print(f"SMA added: {sma_success}")
        
        # Add EMA indicator  
        print("\n2. Adding EMA indicator...")
        ema_success = chart_viewer.add_indicator("EMA_50", "EMA", {"period": 50})
        print(f"EMA added: {ema_success}")
        
        # Add RSI indicator
        print("\n3. Adding RSI indicator...")
        rsi_success = chart_viewer.add_indicator("RSI_14", "RSI", {"period": 14})
        print(f"RSI added: {rsi_success}")
        
        print("\n📊 Current indicators:", len(chart_viewer.current_indicators))
        
        # Test browser refresh simulation to see cache hit logging
        print("\n🔄 Testing browser refresh simulation...")
        chart_viewer2 = ChartViewer()
        
        # Load same data (should trigger indicator restoration and cache hits)
        success2 = chart_viewer2.load_data_n_days_back("EURUSD", "H1", 3)
        if success2:
            print(f"✅ Data loaded in new instance: {len(chart_viewer2.current_data)} candles")
            print(f"📊 Restored indicators: {len(chart_viewer2.current_indicators)}")
            
            # This should show cache hits in terminal
            if len(chart_viewer2.current_indicators) > 0:
                print("✅ Indicators restored with cache hits!")
                return True
            else:
                print("⚠️ No indicators restored")
                return False
        else:
            print("❌ Failed to load data in new instance")
            return False
            
    except Exception as e:
        logger.error(f"Error in terminal debug logging test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run terminal debug logging test."""
    print("🚀 Terminal Debug Logging Test")
    print("=" * 60)
    print("This test will show terminal debug output similar to data loading debug.")
    print("Look for the indicator calculation results in the terminal output below.")
    print("=" * 60)
    
    # Run test
    test_result = test_terminal_debug_logging()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  Terminal Debug Logging: {'✅ PASS' if test_result else '❌ FAIL'}")
    
    print(f"\n🎯 Overall Result: {'✅ TEST PASSED' if test_result else '❌ TEST FAILED'}")
    
    if test_result:
        print("\n🎉 Terminal debug logging is working correctly!")
        print("📌 You should see debug output above showing:")
        print("   - Indicator calculation results")
        print("   - Cache performance metrics")
        print("   - Display classification")
        print("   - Individual indicator details")
    else:
        print("\n🔧 Terminal debug logging needs further fixes.")
    
    return test_result

if __name__ == "__main__":
    main()
