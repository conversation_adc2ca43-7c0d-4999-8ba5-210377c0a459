"""
Comprehensive test suite for Phase 4 implementation.
Tests all performance optimization and export/import features.
"""

import unittest
import tempfile
import shutil
import time
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from pathlib import Path
from datetime import datetime, timedelta
import json

# Import Phase 4 modules
try:
    from performance.compression_manager import CompressionManager, CompressionResult
    from performance.memory_profiler import MemoryProfiler
    from performance.parallel_processor import ParallelProcessor, ProcessingTask
    from performance.performance_monitor import PerformanceMonitor

    from export.chart_exporter import ChartExporter, ExportSettings
    from export.data_exporter import DataExporter, DataExportSettings
    from export.config_manager import ConfigurationManager
    from export.export_manager import ExportManager

    from config.logging_config import get_logger
except ImportError:
    # Fallback for absolute imports
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))

    from performance.compression_manager import CompressionManager, CompressionResult
    from performance.memory_profiler import MemoryProfiler
    from performance.parallel_processor import ParallelProcessor, ProcessingTask
    from performance.performance_monitor import PerformanceMonitor

    from export.chart_exporter import ChartExporter, ExportSettings
    from export.data_exporter import DataExporter, DataExportSettings
    from export.config_manager import ConfigurationManager
    from export.export_manager import ExportManager

    from config.logging_config import get_logger

logger = get_logger(__name__)


class TestPhase4Performance(unittest.TestCase):
    """Test Phase 4.1 - Performance Optimization features."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp())
        
        # Create test data
        dates = pd.date_range('2024-01-01', periods=1000, freq='1H')
        self.test_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 1000),
            'high': np.random.uniform(1.1, 2.1, 1000),
            'low': np.random.uniform(0.9, 1.9, 1000),
            'close': np.random.uniform(1.0, 2.0, 1000),
            'volume': np.random.randint(1000, 10000, 1000)
        }, index=dates)
        
        # Ensure OHLC logic
        for i in range(len(self.test_data)):
            row = self.test_data.iloc[i]
            self.test_data.iloc[i, self.test_data.columns.get_loc('high')] = max(row['open'], row['close']) + 0.01
            self.test_data.iloc[i, self.test_data.columns.get_loc('low')] = min(row['open'], row['close']) - 0.01
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_compression_manager(self):
        """Test compression manager functionality."""
        print("\n🧪 Testing Compression Manager...")
        
        compression_manager = CompressionManager()
        
        # Test different compression algorithms
        algorithms = ['lz4', 'gzip', 'brotli', 'none']
        results = {}
        
        for algorithm in algorithms:
            try:
                compressed_data, result = compression_manager.compress_dataframe(
                    self.test_data, algorithm=algorithm
                )
                
                self.assertIsInstance(result, CompressionResult)
                self.assertTrue(result.success)
                self.assertGreater(result.original_size, 0)
                self.assertGreater(result.compressed_size, 0)
                
                results[algorithm] = result
                print(f"  ✅ {algorithm}: {result.compression_ratio:.2f}x compression")
                
            except Exception as e:
                print(f"  ❌ {algorithm}: {e}")
        
        # Test decompression
        for algorithm, result in results.items():
            if result.success:
                compressed_data, _ = compression_manager.compress_dataframe(
                    self.test_data, algorithm=algorithm
                )
                decompressed_data, success = compression_manager.decompress_dataframe(
                    compressed_data, algorithm
                )
                
                self.assertTrue(success)
                self.assertIsInstance(decompressed_data, pd.DataFrame)
                self.assertEqual(len(decompressed_data), len(self.test_data))
        
        print("  ✅ Compression Manager tests passed")
    
    def test_memory_profiler(self):
        """Test memory profiler functionality."""
        print("\n🧪 Testing Memory Profiler...")
        
        memory_profiler = MemoryProfiler(monitoring_interval=1)
        
        # Test memory usage tracking
        memory_usage = memory_profiler.get_current_memory_usage()
        self.assertIsInstance(memory_usage, dict)
        self.assertIn('system_memory', memory_usage)
        self.assertIn('process_memory', memory_usage)
        
        # Test memory leak detection
        leak_result = memory_profiler.detect_memory_leaks()
        self.assertIsNotNone(leak_result)
        self.assertIsInstance(leak_result.detected, bool)
        
        # Test optimization recommendations
        recommendations = memory_profiler.get_optimization_recommendations()
        self.assertIsInstance(recommendations, list)
        
        print(f"  ✅ Memory usage: {memory_usage['system_memory']['usage_percent']:.1f}%")
        print(f"  ✅ Memory leak detected: {leak_result.detected}")
        print(f"  ✅ Optimization recommendations: {len(recommendations)}")
        print("  ✅ Memory Profiler tests passed")
    
    def test_parallel_processor(self):
        """Test parallel processor functionality."""
        print("\n🧪 Testing Parallel Processor...")
        
        with ParallelProcessor(max_workers=2) as processor:
            # Test simple task submission
            def test_function(x, y):
                time.sleep(0.1)  # Simulate work
                return x + y
            
            task = ProcessingTask(
                task_id="test_task_1",
                function=test_function,
                args=(5, 3),
                kwargs={}
            )
            
            success = processor.submit_task(task)
            self.assertTrue(success)
            
            # Wait for completion
            time.sleep(0.5)
            
            # Check results
            result = processor.results.get("test_task_1")
            self.assertIsNotNone(result)
            self.assertTrue(result.success)
            self.assertEqual(result.result, 8)
            
            # Test performance metrics
            metrics = processor.get_performance_metrics()
            self.assertGreater(metrics.total_tasks, 0)
            
            print(f"  ✅ Task completed: {result.result}")
            print(f"  ✅ Execution time: {result.execution_time:.3f}s")
            print("  ✅ Parallel Processor tests passed")
    
    def test_performance_monitor(self):
        """Test performance monitor functionality."""
        print("\n🧪 Testing Performance Monitor...")
        
        performance_monitor = PerformanceMonitor(monitoring_interval=1)
        
        # Test operation tracking
        performance_monitor.track_operation('test_operation', 0.5)
        performance_monitor.track_operation('test_operation', 0.3)
        
        # Test dashboard data
        dashboard = performance_monitor.get_performance_dashboard()
        self.assertIsInstance(dashboard, dict)
        
        # Test optimization recommendations
        recommendations = performance_monitor.get_optimization_recommendations()
        self.assertIsInstance(recommendations, list)
        
        print(f"  ✅ Dashboard keys: {list(dashboard.keys())}")
        print(f"  ✅ Recommendations: {len(recommendations)}")
        print("  ✅ Performance Monitor tests passed")


class TestPhase4Export(unittest.TestCase):
    """Test Phase 4.2 - Export & Import features."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp())
        
        # Create test data
        dates = pd.date_range('2024-01-01', periods=100, freq='1H')
        self.test_data = pd.DataFrame({
            'open': np.random.uniform(1.0, 2.0, 100),
            'high': np.random.uniform(1.1, 2.1, 100),
            'low': np.random.uniform(0.9, 1.9, 100),
            'close': np.random.uniform(1.0, 2.0, 100),
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # Create test chart
        self.test_figure = go.Figure()
        self.test_figure.add_trace(go.Candlestick(
            x=self.test_data.index,
            open=self.test_data['open'],
            high=self.test_data['high'],
            low=self.test_data['low'],
            close=self.test_data['close'],
            name='Test Data'
        ))
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_chart_exporter(self):
        """Test chart exporter functionality."""
        print("\n🧪 Testing Chart Exporter...")
        
        chart_exporter = ChartExporter()
        
        # Test different export formats
        formats = ['png', 'svg', 'html']
        
        for format_type in formats:
            try:
                settings = ExportSettings(
                    format=format_type,
                    width=800,
                    height=600,
                    title="Test Chart",
                    watermark="Test Export"
                )
                
                output_path = self.test_dir / f"test_chart.{format_type}"
                result = chart_exporter.export_chart(self.test_figure, settings, output_path)
                
                self.assertTrue(result.success)
                self.assertTrue(output_path.exists())
                self.assertGreater(result.file_size_mb, 0)
                
                print(f"  ✅ {format_type.upper()}: {result.file_size_mb:.2f} MB")
                
            except Exception as e:
                print(f"  ❌ {format_type.upper()}: {e}")
        
        print("  ✅ Chart Exporter tests passed")
    
    def test_data_exporter(self):
        """Test data exporter functionality."""
        print("\n🧪 Testing Data Exporter...")
        
        data_exporter = DataExporter()
        
        # Test different export formats
        formats = ['csv', 'json', 'excel', 'parquet']
        
        for format_type in formats:
            try:
                settings = DataExportSettings(
                    format=format_type,
                    include_metadata=True,
                    decimal_places=4
                )
                
                output_path = self.test_dir / f"test_data.{format_type}"
                result = data_exporter.export_data(self.test_data, None, settings, output_path)
                
                self.assertTrue(result.success)
                self.assertTrue(output_path.exists())
                self.assertEqual(result.rows_exported, len(self.test_data))
                self.assertGreater(result.file_size_mb, 0)
                
                print(f"  ✅ {format_type.upper()}: {result.rows_exported} rows, {result.file_size_mb:.3f} MB")
                
            except Exception as e:
                print(f"  ❌ {format_type.upper()}: {e}")
        
        print("  ✅ Data Exporter tests passed")
    
    def test_configuration_manager(self):
        """Test configuration manager functionality."""
        print("\n🧪 Testing Configuration Manager...")
        
        # Use test directory for config manager
        config_manager = ConfigurationManager()
        config_manager.workspaces_dir = self.test_dir / 'workspaces'
        config_manager.presets_dir = self.test_dir / 'presets'
        config_manager.workspaces_dir.mkdir(parents=True, exist_ok=True)
        config_manager.presets_dir.mkdir(parents=True, exist_ok=True)
        
        # Test workspace save/load
        test_workspace = {
            'pair': 'EURUSD',
            'timeframe': 'H1',
            'days_back': 5,
            'indicators': {'SMA_20': {'indicator_type': 'sma', 'parameters': {'period': 20}}},
            'chart_style': 'Candlestick',
            'chart_theme': 'plotly_white',
            'chart_height': 600,
            'author': 'Test User'
        }
        
        # Save workspace
        success = config_manager.save_workspace(
            name="Test Workspace",
            description="Test workspace for unit testing",
            current_state=test_workspace,
            tags=['test', 'unit-test']
        )
        self.assertTrue(success)
        
        # Load workspace
        loaded_workspace = config_manager.load_workspace("Test Workspace")
        self.assertIsNotNone(loaded_workspace)
        self.assertEqual(loaded_workspace.name, "Test Workspace")
        self.assertEqual(loaded_workspace.pair, "EURUSD")
        
        # List workspaces
        workspaces = config_manager.list_workspaces()
        self.assertGreater(len(workspaces), 0)
        
        # Test indicator presets
        presets = config_manager.list_indicator_presets()
        self.assertGreater(len(presets), 0)  # Should have built-in presets
        
        # Load built-in preset
        trend_preset = config_manager.load_indicator_preset("trend_analysis")
        self.assertIsNotNone(trend_preset)
        
        print(f"  ✅ Workspace saved and loaded: {loaded_workspace.name}")
        print(f"  ✅ Available workspaces: {len(workspaces)}")
        print(f"  ✅ Available presets: {len(presets)}")
        print("  ✅ Configuration Manager tests passed")
    
    def test_export_manager(self):
        """Test unified export manager functionality."""
        print("\n🧪 Testing Export Manager...")
        
        export_manager = ExportManager()
        
        # Test single chart export
        chart_result = export_manager.export_chart(
            self.test_figure,
            template='quick_share',
            output_path=self.test_dir / 'test_chart_manager.png'
        )
        self.assertIsNotNone(chart_result)
        self.assertTrue(chart_result.success)
        
        # Test single data export
        data_result = export_manager.export_data(
            self.test_data,
            template='data_analysis',
            output_path=self.test_dir / 'test_data_manager.csv'
        )
        self.assertIsNotNone(data_result)
        self.assertTrue(data_result.success)
        
        # Test export templates
        templates = export_manager.get_export_templates()
        self.assertGreater(len(templates), 0)
        
        # Test export statistics
        stats = export_manager.get_export_statistics()
        self.assertIsInstance(stats, dict)
        
        print(f"  ✅ Chart export: {chart_result.file_size_mb:.3f} MB")
        print(f"  ✅ Data export: {data_result.rows_exported} rows")
        print(f"  ✅ Available templates: {len(templates)}")
        print("  ✅ Export Manager tests passed")


def run_phase4_tests():
    """Run all Phase 4 tests."""
    print("🚀 Starting Phase 4 Implementation Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add performance tests
    test_suite.addTest(TestPhase4Performance('test_compression_manager'))
    test_suite.addTest(TestPhase4Performance('test_memory_profiler'))
    test_suite.addTest(TestPhase4Performance('test_parallel_processor'))
    test_suite.addTest(TestPhase4Performance('test_performance_monitor'))
    
    # Add export tests
    test_suite.addTest(TestPhase4Export('test_chart_exporter'))
    test_suite.addTest(TestPhase4Export('test_data_exporter'))
    test_suite.addTest(TestPhase4Export('test_configuration_manager'))
    test_suite.addTest(TestPhase4Export('test_export_manager'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎯 Phase 4 Implementation Test Summary")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n✅ Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🏆 All Phase 4 tests passed! Ready for production.")
    elif success_rate >= 80:
        print("⚠️  Most tests passed. Review failures before proceeding.")
    else:
        print("❌ Multiple test failures. Requires investigation.")
    
    return result


if __name__ == "__main__":
    run_phase4_tests()
