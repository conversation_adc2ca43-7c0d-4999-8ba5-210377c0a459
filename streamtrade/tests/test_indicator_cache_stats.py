#!/usr/bin/env python3
"""
Test script for Indicator Cache Statistics
Tests if indicator cache statistics are working correctly.
"""

import sys
import os
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.data.enhanced_data_manager import EnhancedDataManager
from streamtrade.config.logging_config import get_logger

logger = get_logger(__name__)

def test_indicator_cache_stats():
    """Test indicator cache statistics functionality."""
    print("🧪 Testing Indicator Cache Statistics...")
    
    try:
        # Initialize data manager
        data_manager = EnhancedDataManager()
        
        print(f"✅ Data manager initialized")
        print(f"📊 Has indicator cache: {hasattr(data_manager, 'indicator_cache')}")
        
        if hasattr(data_manager, 'indicator_cache') and data_manager.indicator_cache:
            print(f"✅ Indicator cache available")
            print(f"📊 Has get_indicator_stats: {hasattr(data_manager.indicator_cache, 'get_indicator_stats')}")
            
            if hasattr(data_manager.indicator_cache, 'get_indicator_stats'):
                # Get indicator statistics
                print("\n📊 Getting indicator cache statistics...")
                print(f"DEBUG: About to call get_indicator_stats on {data_manager.indicator_cache}")
                stats = data_manager.indicator_cache.get_indicator_stats()
                print(f"DEBUG: get_indicator_stats returned: {stats}")
                
                print(f"\n📋 Indicator Cache Statistics:")
                print(f"   📊 Total Indicators: {stats.get('total_indicators', 0)}")
                print(f"   📁 Cache Files: {stats.get('total_files', 0)}")
                print(f"   💾 Total Size: {stats.get('total_size_mb', 0):.3f} MB")
                print(f"   📂 Status: {stats.get('status', 'Unknown')}")
                print(f"   📍 Directory: {stats.get('cache_directory', 'N/A')}")
                
                # Show individual files if any
                indicator_files = stats.get('indicator_files', [])
                if indicator_files:
                    print(f"\n📋 Cache Files ({len(indicator_files)}):")
                    for i, file_info in enumerate(indicator_files[:5]):  # Show max 5 files
                        file_name = file_info.get('file', 'Unknown')
                        file_size_mb = file_info.get('size_mb', 0)
                        print(f"   {i+1}. {file_name}: {file_size_mb:.3f} MB")
                    
                    if len(indicator_files) > 5:
                        print(f"   ... and {len(indicator_files) - 5} more files")
                else:
                    print(f"\n📭 No cache files found")
                
                # Check if cache directory exists
                cache_dir = Path(stats.get('cache_directory', ''))
                if cache_dir.exists():
                    print(f"\n📂 Cache Directory Analysis:")
                    print(f"   📍 Path: {cache_dir}")
                    print(f"   📁 Exists: {cache_dir.exists()}")
                    print(f"   📊 Is Directory: {cache_dir.is_dir()}")
                    
                    # Count actual files
                    actual_files = list(cache_dir.glob('*.parquet'))
                    print(f"   📋 Actual .parquet files: {len(actual_files)}")
                    
                    if actual_files:
                        total_actual_size = sum(f.stat().st_size for f in actual_files)
                        total_actual_size_mb = total_actual_size / (1024 * 1024)
                        print(f"   💾 Actual total size: {total_actual_size_mb:.3f} MB")
                else:
                    print(f"\n❌ Cache directory does not exist: {cache_dir}")
                
                return True
            else:
                print("❌ get_indicator_stats method not available")
                return False
        else:
            print("❌ Indicator cache not available")
            return False
            
    except Exception as e:
        logger.error(f"Error in indicator cache stats test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def test_cache_directory_structure():
    """Test cache directory structure."""
    print("\n🧪 Testing Cache Directory Structure...")
    
    try:
        # Check cache directory structure
        cache_base = Path(__file__).parent.parent / 'cache'
        indicator_cache_dir = cache_base / 'data' / 'indicators'
        
        print(f"📂 Cache Structure Analysis:")
        print(f"   📍 Base cache: {cache_base}")
        print(f"   📁 Base exists: {cache_base.exists()}")
        print(f"   📍 Indicator cache: {indicator_cache_dir}")
        print(f"   📁 Indicator exists: {indicator_cache_dir.exists()}")
        
        if indicator_cache_dir.exists():
            # List all files
            all_files = list(indicator_cache_dir.iterdir())
            parquet_files = list(indicator_cache_dir.glob('*.parquet'))
            
            print(f"   📋 Total files: {len(all_files)}")
            print(f"   📊 Parquet files: {len(parquet_files)}")
            
            if parquet_files:
                print(f"\n📋 Parquet Files:")
                for i, file_path in enumerate(parquet_files[:5]):
                    file_size = file_path.stat().st_size
                    file_size_mb = file_size / (1024 * 1024)
                    print(f"   {i+1}. {file_path.name}: {file_size_mb:.3f} MB")
                
                if len(parquet_files) > 5:
                    print(f"   ... and {len(parquet_files) - 5} more files")
            
            return True
        else:
            print(f"   ❌ Indicator cache directory does not exist")
            return False
            
    except Exception as e:
        logger.error(f"Error in cache directory test: {e}")
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Run all indicator cache statistics tests."""
    print("🚀 Indicator Cache Statistics Tests")
    print("=" * 60)
    
    # Test 1: Indicator cache statistics
    test1_result = test_indicator_cache_stats()
    
    # Test 2: Cache directory structure
    test2_result = test_cache_directory_structure()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  1. Indicator Cache Stats: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  2. Cache Directory Structure: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    overall_success = test1_result and test2_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Indicator cache statistics are working correctly!")
        print("📌 Key Features Validated:")
        print("   - Cache statistics method available")
        print("   - File counting and size calculation working")
        print("   - Cache directory structure correct")
        print("   - Individual file information available")
    else:
        print("\n🔧 Indicator cache statistics need further fixes.")
    
    return overall_success

if __name__ == "__main__":
    main()
