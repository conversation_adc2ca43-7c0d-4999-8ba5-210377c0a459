#!/usr/bin/env python3
"""
Phase 4 Implementation Test Runner
Comprehensive testing for Performance Optimization and Export/Import features.
"""

import sys
import os
from pathlib import Path

# Add the streamtrade directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set environment variables
os.environ['PYTHONPATH'] = str(current_dir)

def main():
    """Main test runner function."""
    print("🚀 Lionaire Platform - Phase 4 Implementation Tests")
    print("=" * 70)
    print("Testing Performance Optimization and Export/Import features")
    print("=" * 70)
    
    try:
        # Import and run tests
        from tests.test_phase4_implementation import run_phase4_tests
        
        print("📋 Test Categories:")
        print("  • Phase 4.1 - Performance Optimization")
        print("    - Compression Manager")
        print("    - Memory Profiler") 
        print("    - Parallel Processor")
        print("    - Performance Monitor")
        print("")
        print("  • Phase 4.2 - Export & Import")
        print("    - Chart Exporter")
        print("    - Data Exporter")
        print("    - Configuration Manager")
        print("    - Export Manager")
        print("")
        
        # Run the tests
        result = run_phase4_tests()
        
        # Return appropriate exit code
        if result.wasSuccessful():
            print("\n🎉 All tests completed successfully!")
            return 0
        else:
            print(f"\n⚠️  Some tests failed. Please review the output above.")
            return 1
            
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\nPlease ensure all dependencies are installed:")
        print("pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"💥 Unexpected Error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
