"""
Advanced Chart Exporter for Lionaire platform.
Phase 4.2.1 - Advanced Chart Export.

Features:
- High-resolution PNG export (300+ DPI)
- Vector SVG export with custom styling
- Interactive HTML with embedded data
- PDF export for reports
- Export customization and templates
"""

import io
import base64
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
import plotly.graph_objects as go
import plotly.io as pio
from dataclasses import dataclass

from ..config.logging_config import get_logger
from ..config.user_settings import get_user_settings

logger = get_logger(__name__)


@dataclass
class ExportSettings:
    """Chart export settings."""
    format: str  # 'png', 'svg', 'html', 'pdf'
    width: int = 1920
    height: int = 1080
    dpi: int = 300
    quality: int = 95  # For JPEG/PNG
    include_data: bool = True
    include_indicators: bool = True
    watermark: Optional[str] = None
    title: Optional[str] = None
    subtitle: Optional[str] = None
    theme: str = 'plotly_white'
    custom_css: Optional[str] = None


@dataclass
class ExportResult:
    """Chart export result."""
    success: bool
    file_path: Optional[Path]
    file_size_mb: float
    export_time: float
    format: str
    error_message: Optional[str] = None


class ChartExporter:
    """
    Advanced chart exporter with multiple formats and customization.
    """
    
    def __init__(self):
        """Initialize Chart Exporter."""
        self.user_settings = get_user_settings()
        
        # Export templates
        self.templates = {
            'professional': {
                'theme': 'plotly_white',
                'font_family': 'Arial, sans-serif',
                'font_size': 12,
                'title_font_size': 16,
                'watermark': 'Lionaire Trading Platform'
            },
            'presentation': {
                'theme': 'plotly_white',
                'font_family': 'Helvetica, sans-serif',
                'font_size': 14,
                'title_font_size': 20,
                'watermark': None
            },
            'report': {
                'theme': 'plotly_white',
                'font_family': 'Times New Roman, serif',
                'font_size': 10,
                'title_font_size': 14,
                'watermark': 'Generated by Lionaire'
            },
            'dark': {
                'theme': 'plotly_dark',
                'font_family': 'Arial, sans-serif',
                'font_size': 12,
                'title_font_size': 16,
                'watermark': 'Lionaire Trading Platform'
            }
        }
        
        # Supported formats
        self.supported_formats = ['png', 'svg', 'html', 'pdf', 'jpeg']
        
        logger.info("Chart Exporter initialized")
    
    def export_chart(
        self,
        figure: go.Figure,
        settings: ExportSettings,
        output_path: Optional[Path] = None
    ) -> ExportResult:
        """
        Export chart with specified settings.
        
        Args:
            figure: Plotly figure to export
            settings: Export settings
            output_path: Output file path (optional)
            
        Returns:
            Export result
        """
        start_time = datetime.now()
        
        try:
            # Validate format
            if settings.format not in self.supported_formats:
                raise ValueError(f"Unsupported format: {settings.format}")
            
            # Prepare figure
            prepared_figure = self._prepare_figure(figure, settings)
            
            # Generate output path if not provided
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"chart_export_{timestamp}.{settings.format}"
                output_path = Path(filename)
            
            # Export based on format
            if settings.format == 'png':
                success = self._export_png(prepared_figure, settings, output_path)
            elif settings.format == 'svg':
                success = self._export_svg(prepared_figure, settings, output_path)
            elif settings.format == 'html':
                success = self._export_html(prepared_figure, settings, output_path)
            elif settings.format == 'pdf':
                success = self._export_pdf(prepared_figure, settings, output_path)
            elif settings.format == 'jpeg':
                success = self._export_jpeg(prepared_figure, settings, output_path)
            else:
                raise ValueError(f"Export method not implemented for: {settings.format}")
            
            if not success:
                raise Exception("Export failed")
            
            # Calculate file size
            file_size_mb = output_path.stat().st_size / 1024 / 1024 if output_path.exists() else 0
            export_time = (datetime.now() - start_time).total_seconds()
            
            result = ExportResult(
                success=True,
                file_path=output_path,
                file_size_mb=file_size_mb,
                export_time=export_time,
                format=settings.format
            )
            
            logger.info(f"Chart exported successfully: {output_path} ({file_size_mb:.2f} MB)")
            return result
            
        except Exception as e:
            export_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Chart export failed: {e}")
            
            return ExportResult(
                success=False,
                file_path=None,
                file_size_mb=0,
                export_time=export_time,
                format=settings.format,
                error_message=str(e)
            )
    
    def _prepare_figure(self, figure: go.Figure, settings: ExportSettings) -> go.Figure:
        """Prepare figure for export with customizations."""
        try:
            # Create a copy to avoid modifying original
            fig = go.Figure(figure)
            
            # Apply theme
            fig.update_layout(template=settings.theme)
            
            # Set dimensions
            fig.update_layout(
                width=settings.width,
                height=settings.height
            )
            
            # Add title and subtitle
            if settings.title:
                title_text = settings.title
                if settings.subtitle:
                    title_text += f"<br><sub>{settings.subtitle}</sub>"
                
                fig.update_layout(
                    title={
                        'text': title_text,
                        'x': 0.5,
                        'xanchor': 'center',
                        'font': {'size': 16}
                    }
                )
            
            # Add watermark
            if settings.watermark:
                fig.add_annotation(
                    text=settings.watermark,
                    xref="paper", yref="paper",
                    x=0.99, y=0.01,
                    xanchor='right', yanchor='bottom',
                    showarrow=False,
                    font=dict(size=10, color="rgba(128,128,128,0.5)"),
                    bgcolor="rgba(255,255,255,0.8)",
                    bordercolor="rgba(128,128,128,0.3)",
                    borderwidth=1
                )
            
            # Optimize for export
            fig.update_layout(
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=1.02,
                    xanchor="right",
                    x=1
                ),
                margin=dict(l=50, r=50, t=80, b=50)
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"Error preparing figure: {e}")
            return figure
    
    def _export_png(self, figure: go.Figure, settings: ExportSettings, output_path: Path) -> bool:
        """Export as high-resolution PNG."""
        try:
            # Configure PNG export
            pio.write_image(
                figure,
                output_path,
                format='png',
                width=settings.width,
                height=settings.height,
                scale=settings.dpi / 72  # Convert DPI to scale factor
            )
            return True
            
        except Exception as e:
            logger.error(f"PNG export failed: {e}")
            return False
    
    def _export_svg(self, figure: go.Figure, settings: ExportSettings, output_path: Path) -> bool:
        """Export as vector SVG."""
        try:
            # Export SVG
            svg_content = pio.to_image(
                figure,
                format='svg',
                width=settings.width,
                height=settings.height
            )
            
            # Apply custom CSS if provided
            if settings.custom_css:
                svg_content = self._apply_custom_css(svg_content, settings.custom_css)
            
            # Write to file
            with open(output_path, 'wb') as f:
                f.write(svg_content)
            
            return True
            
        except Exception as e:
            logger.error(f"SVG export failed: {e}")
            return False
    
    def _export_html(self, figure: go.Figure, settings: ExportSettings, output_path: Path) -> bool:
        """Export as interactive HTML."""
        try:
            # Configure HTML export
            config = {
                'displayModeBar': True,
                'displaylogo': False,
                'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d'],
                'toImageButtonOptions': {
                    'format': 'png',
                    'filename': 'chart',
                    'height': settings.height,
                    'width': settings.width,
                    'scale': 2
                }
            }
            
            # Generate HTML
            html_content = pio.to_html(
                figure,
                config=config,
                include_plotlyjs=True,
                div_id="chart-container"
            )
            
            # Add custom styling
            if settings.custom_css:
                html_content = self._add_html_styling(html_content, settings.custom_css)
            
            # Embed data if requested
            if settings.include_data:
                html_content = self._embed_chart_data(html_content, figure)
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return True
            
        except Exception as e:
            logger.error(f"HTML export failed: {e}")
            return False
    
    def _export_pdf(self, figure: go.Figure, settings: ExportSettings, output_path: Path) -> bool:
        """Export as PDF."""
        try:
            # For PDF export, we'll use the PNG method and convert
            # This requires additional dependencies like kaleido
            pio.write_image(
                figure,
                output_path,
                format='pdf',
                width=settings.width,
                height=settings.height
            )
            return True
            
        except Exception as e:
            logger.error(f"PDF export failed: {e}")
            # Fallback to PNG if PDF fails
            try:
                png_path = output_path.with_suffix('.png')
                success = self._export_png(figure, settings, png_path)
                if success:
                    logger.warning(f"PDF export failed, saved as PNG: {png_path}")
                return success
            except:
                return False
    
    def _export_jpeg(self, figure: go.Figure, settings: ExportSettings, output_path: Path) -> bool:
        """Export as JPEG."""
        try:
            pio.write_image(
                figure,
                output_path,
                format='jpeg',
                width=settings.width,
                height=settings.height,
                scale=settings.dpi / 72
            )
            return True
            
        except Exception as e:
            logger.error(f"JPEG export failed: {e}")
            return False
    
    def _apply_custom_css(self, svg_content: bytes, custom_css: str) -> bytes:
        """Apply custom CSS to SVG content."""
        try:
            svg_str = svg_content.decode('utf-8')
            
            # Insert CSS into SVG
            css_style = f"<style><![CDATA[{custom_css}]]></style>"
            svg_str = svg_str.replace('<svg', f'<svg>{css_style}<svg', 1)
            
            return svg_str.encode('utf-8')
            
        except Exception as e:
            logger.error(f"Error applying custom CSS: {e}")
            return svg_content
    
    def _add_html_styling(self, html_content: str, custom_css: str) -> str:
        """Add custom styling to HTML content."""
        try:
            # Insert CSS into HTML head
            css_block = f"<style>{custom_css}</style>"
            html_content = html_content.replace('</head>', f'{css_block}</head>')
            
            return html_content
            
        except Exception as e:
            logger.error(f"Error adding HTML styling: {e}")
            return html_content
    
    def _embed_chart_data(self, html_content: str, figure: go.Figure) -> str:
        """Embed chart data in HTML for offline use."""
        try:
            # Extract data from figure
            chart_data = {
                'data': [trace.to_plotly_json() for trace in figure.data],
                'layout': figure.layout.to_plotly_json(),
                'config': figure.to_dict().get('config', {})
            }
            
            # Embed as JavaScript variable
            data_script = f"""
            <script>
            window.chartData = {json.dumps(chart_data, default=str)};
            </script>
            """
            
            html_content = html_content.replace('</head>', f'{data_script}</head>')
            
            return html_content
            
        except Exception as e:
            logger.error(f"Error embedding chart data: {e}")
            return html_content
    
    def get_export_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get available export templates."""
        return self.templates.copy()
    
    def apply_template(self, settings: ExportSettings, template_name: str) -> ExportSettings:
        """Apply export template to settings."""
        try:
            if template_name not in self.templates:
                logger.warning(f"Unknown template: {template_name}")
                return settings
            
            template = self.templates[template_name]
            
            # Apply template settings
            settings.theme = template.get('theme', settings.theme)
            if template.get('watermark'):
                settings.watermark = template['watermark']
            
            return settings
            
        except Exception as e:
            logger.error(f"Error applying template: {e}")
            return settings
    
    def batch_export(
        self,
        figures: List[Tuple[go.Figure, str]],  # (figure, name) pairs
        settings: ExportSettings,
        output_dir: Path
    ) -> List[ExportResult]:
        """Export multiple charts in batch."""
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            results = []
            
            for i, (figure, name) in enumerate(figures):
                output_path = output_dir / f"{name}.{settings.format}"
                result = self.export_chart(figure, settings, output_path)
                results.append(result)
                
                logger.info(f"Batch export progress: {i+1}/{len(figures)}")
            
            successful_exports = sum(1 for r in results if r.success)
            logger.info(f"Batch export completed: {successful_exports}/{len(figures)} successful")
            
            return results

        except Exception as e:
            logger.error(f"Batch export failed: {e}")
            return []
