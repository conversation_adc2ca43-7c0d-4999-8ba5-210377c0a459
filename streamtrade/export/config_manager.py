"""
Configuration Management System for Lionaire platform.
Phase 4.2.3 - Configuration Management System.

Features:
- Complete workspace save/restore
- Multiple workspace profiles
- Workspace sharing capabilities
- Version control for configurations
- Indicator presets management
"""

import json
import shutil
import zipfile
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
import hashlib

from ..config.logging_config import get_logger
from ..config.user_settings import get_user_settings
from ..indicators.indicator_manager import IndicatorManager

logger = get_logger(__name__)


@dataclass
class WorkspaceConfig:
    """Complete workspace configuration."""
    name: str
    description: str
    version: str
    created_at: datetime
    modified_at: datetime
    
    # Data settings
    pair: str
    timeframe: str
    days_back: int
    
    # Indicator configurations
    indicators: Dict[str, Any]
    
    # Chart settings
    chart_style: str
    chart_theme: str
    chart_height: int
    
    # User preferences
    user_settings: Dict[str, Any]
    
    # Metadata
    tags: List[str]
    author: str
    checksum: str


@dataclass
class IndicatorPreset:
    """Indicator preset configuration."""
    name: str
    description: str
    category: str
    indicators: Dict[str, Any]
    created_at: datetime
    author: str
    tags: List[str]
    use_count: int = 0


@dataclass
class ConfigExportResult:
    """Configuration export result."""
    success: bool
    file_path: Optional[Path]
    file_size_mb: float
    export_time: float
    items_exported: int
    error_message: Optional[str] = None


class ConfigurationManager:
    """
    Advanced configuration management with workspace and preset support.
    """
    
    def __init__(self):
        """Initialize Configuration Manager."""
        self.user_settings = get_user_settings()
        
        # Configuration directories
        self.config_dir = Path(__file__).parent.parent / 'config'
        self.workspaces_dir = self.config_dir / 'workspaces'
        self.presets_dir = self.config_dir / 'presets'
        self.exports_dir = self.config_dir / 'exports'
        
        # Create directories
        for directory in [self.workspaces_dir, self.presets_dir, self.exports_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Built-in presets
        self.builtin_presets = {
            'trend_analysis': {
                'name': 'Trend Analysis',
                'description': 'Moving averages and trend indicators',
                'category': 'trend',
                'indicators': {
                    'SMA_20': {'indicator_type': 'sma', 'parameters': {'period': 20}},
                    'SMA_50': {'indicator_type': 'sma', 'parameters': {'period': 50}},
                    'EMA_12': {'indicator_type': 'ema', 'parameters': {'period': 12}},
                    'EMA_26': {'indicator_type': 'ema', 'parameters': {'period': 26}}
                }
            },
            'momentum_analysis': {
                'name': 'Momentum Analysis',
                'description': 'RSI, MACD and momentum indicators',
                'category': 'momentum',
                'indicators': {
                    'RSI': {'indicator_type': 'rsi', 'parameters': {'period': 14}},
                    'MACD': {'indicator_type': 'macd', 'parameters': {'fast': 12, 'slow': 26, 'signal': 9}},
                    'Stochastic': {'indicator_type': 'stochastic', 'parameters': {'k_period': 14, 'd_period': 3}}
                }
            },
            'volatility_analysis': {
                'name': 'Volatility Analysis',
                'description': 'Bollinger Bands and volatility indicators',
                'category': 'volatility',
                'indicators': {
                    'Bollinger_Bands': {'indicator_type': 'bollinger_bands', 'parameters': {'period': 20, 'std_dev': 2}},
                    'ATR': {'indicator_type': 'atr', 'parameters': {'period': 14}}
                }
            },
            'complete_analysis': {
                'name': 'Complete Analysis',
                'description': 'Comprehensive indicator set for full market analysis',
                'category': 'comprehensive',
                'indicators': {
                    'SMA_20': {'indicator_type': 'sma', 'parameters': {'period': 20}},
                    'EMA_12': {'indicator_type': 'ema', 'parameters': {'period': 12}},
                    'RSI': {'indicator_type': 'rsi', 'parameters': {'period': 14}},
                    'MACD': {'indicator_type': 'macd', 'parameters': {'fast': 12, 'slow': 26, 'signal': 9}},
                    'Bollinger_Bands': {'indicator_type': 'bollinger_bands', 'parameters': {'period': 20, 'std_dev': 2}}
                }
            }
        }
        
        logger.info("Configuration Manager initialized")
    
    def save_workspace(
        self,
        name: str,
        description: str,
        current_state: Dict[str, Any],
        tags: Optional[List[str]] = None
    ) -> bool:
        """
        Save current workspace configuration.
        
        Args:
            name: Workspace name
            description: Workspace description
            current_state: Current application state
            tags: Optional tags
            
        Returns:
            True if saved successfully
        """
        try:
            # Create workspace configuration
            workspace = WorkspaceConfig(
                name=name,
                description=description,
                version='1.0.0',
                created_at=datetime.now(),
                modified_at=datetime.now(),
                pair=current_state.get('pair', 'EURUSD'),
                timeframe=current_state.get('timeframe', 'H1'),
                days_back=current_state.get('days_back', 5),
                indicators=current_state.get('indicators', {}),
                chart_style=current_state.get('chart_style', 'Candlestick'),
                chart_theme=current_state.get('chart_theme', 'plotly_white'),
                chart_height=current_state.get('chart_height', 600),
                user_settings=self.user_settings.get_all_settings(),
                tags=tags or [],
                author=current_state.get('author', 'User'),
                checksum=''
            )
            
            # Calculate checksum
            workspace_dict = asdict(workspace)
            workspace_dict.pop('checksum')  # Remove checksum from calculation
            workspace_str = json.dumps(workspace_dict, sort_keys=True, default=str)
            workspace.checksum = hashlib.md5(workspace_str.encode()).hexdigest()
            
            # Save to file
            workspace_file = self.workspaces_dir / f"{name.replace(' ', '_')}.json"
            with open(workspace_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(workspace), f, indent=2, default=str)
            
            logger.info(f"Workspace saved: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving workspace: {e}")
            return False
    
    def load_workspace(self, name: str) -> Optional[WorkspaceConfig]:
        """
        Load workspace configuration.
        
        Args:
            name: Workspace name
            
        Returns:
            Workspace configuration or None
        """
        try:
            workspace_file = self.workspaces_dir / f"{name.replace(' ', '_')}.json"
            
            if not workspace_file.exists():
                logger.error(f"Workspace not found: {name}")
                return None
            
            with open(workspace_file, 'r', encoding='utf-8') as f:
                workspace_data = json.load(f)
            
            # Convert datetime strings back to datetime objects
            workspace_data['created_at'] = datetime.fromisoformat(workspace_data['created_at'])
            workspace_data['modified_at'] = datetime.fromisoformat(workspace_data['modified_at'])
            
            workspace = WorkspaceConfig(**workspace_data)
            
            # Verify checksum
            workspace_dict = asdict(workspace)
            stored_checksum = workspace_dict.pop('checksum')
            workspace_str = json.dumps(workspace_dict, sort_keys=True, default=str)
            calculated_checksum = hashlib.md5(workspace_str.encode()).hexdigest()
            
            if stored_checksum != calculated_checksum:
                logger.warning(f"Workspace checksum mismatch: {name}")
            
            logger.info(f"Workspace loaded: {name}")
            return workspace
            
        except Exception as e:
            logger.error(f"Error loading workspace: {e}")
            return None
    
    def list_workspaces(self) -> List[Dict[str, Any]]:
        """List all available workspaces."""
        try:
            workspaces = []
            
            for workspace_file in self.workspaces_dir.glob('*.json'):
                try:
                    with open(workspace_file, 'r', encoding='utf-8') as f:
                        workspace_data = json.load(f)
                    
                    workspaces.append({
                        'name': workspace_data['name'],
                        'description': workspace_data['description'],
                        'created_at': workspace_data['created_at'],
                        'modified_at': workspace_data['modified_at'],
                        'tags': workspace_data.get('tags', []),
                        'author': workspace_data.get('author', 'Unknown'),
                        'file_path': str(workspace_file)
                    })
                    
                except Exception as e:
                    logger.warning(f"Error reading workspace file {workspace_file}: {e}")
            
            # Sort by modification date (newest first)
            workspaces.sort(key=lambda x: x['modified_at'], reverse=True)
            
            return workspaces
            
        except Exception as e:
            logger.error(f"Error listing workspaces: {e}")
            return []
    
    def delete_workspace(self, name: str) -> bool:
        """Delete workspace."""
        try:
            workspace_file = self.workspaces_dir / f"{name.replace(' ', '_')}.json"
            
            if workspace_file.exists():
                workspace_file.unlink()
                logger.info(f"Workspace deleted: {name}")
                return True
            else:
                logger.error(f"Workspace not found: {name}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting workspace: {e}")
            return False
    
    def save_indicator_preset(
        self,
        name: str,
        description: str,
        category: str,
        indicators: Dict[str, Any],
        tags: Optional[List[str]] = None
    ) -> bool:
        """Save indicator preset."""
        try:
            preset = IndicatorPreset(
                name=name,
                description=description,
                category=category,
                indicators=indicators,
                created_at=datetime.now(),
                author='User',
                tags=tags or [],
                use_count=0
            )
            
            preset_file = self.presets_dir / f"{name.replace(' ', '_')}.json"
            with open(preset_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(preset), f, indent=2, default=str)
            
            logger.info(f"Indicator preset saved: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving indicator preset: {e}")
            return False
    
    def load_indicator_preset(self, name: str) -> Optional[IndicatorPreset]:
        """Load indicator preset."""
        try:
            # Check custom presets first
            preset_file = self.presets_dir / f"{name.replace(' ', '_')}.json"
            
            if preset_file.exists():
                with open(preset_file, 'r', encoding='utf-8') as f:
                    preset_data = json.load(f)
                
                preset_data['created_at'] = datetime.fromisoformat(preset_data['created_at'])
                return IndicatorPreset(**preset_data)
            
            # Check built-in presets
            elif name in self.builtin_presets:
                builtin = self.builtin_presets[name]
                return IndicatorPreset(
                    name=builtin['name'],
                    description=builtin['description'],
                    category=builtin['category'],
                    indicators=builtin['indicators'],
                    created_at=datetime.now(),
                    author='Lionaire',
                    tags=['built-in'],
                    use_count=0
                )
            
            else:
                logger.error(f"Indicator preset not found: {name}")
                return None
                
        except Exception as e:
            logger.error(f"Error loading indicator preset: {e}")
            return None
    
    def list_indicator_presets(self) -> List[Dict[str, Any]]:
        """List all indicator presets."""
        try:
            presets = []
            
            # Add built-in presets
            for name, builtin in self.builtin_presets.items():
                presets.append({
                    'name': builtin['name'],
                    'description': builtin['description'],
                    'category': builtin['category'],
                    'author': 'Lionaire',
                    'tags': ['built-in'],
                    'indicator_count': len(builtin['indicators']),
                    'is_builtin': True
                })
            
            # Add custom presets
            for preset_file in self.presets_dir.glob('*.json'):
                try:
                    with open(preset_file, 'r', encoding='utf-8') as f:
                        preset_data = json.load(f)
                    
                    presets.append({
                        'name': preset_data['name'],
                        'description': preset_data['description'],
                        'category': preset_data['category'],
                        'author': preset_data.get('author', 'User'),
                        'tags': preset_data.get('tags', []),
                        'indicator_count': len(preset_data['indicators']),
                        'is_builtin': False,
                        'use_count': preset_data.get('use_count', 0)
                    })
                    
                except Exception as e:
                    logger.warning(f"Error reading preset file {preset_file}: {e}")
            
            return presets
            
        except Exception as e:
            logger.error(f"Error listing indicator presets: {e}")
            return []
    
    def export_workspace(self, workspace_name: str, output_path: Optional[Path] = None) -> ConfigExportResult:
        """Export workspace as shareable package."""
        start_time = datetime.now()
        
        try:
            workspace = self.load_workspace(workspace_name)
            if not workspace:
                raise ValueError(f"Workspace not found: {workspace_name}")
            
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = self.exports_dir / f"{workspace_name}_{timestamp}.zip"
            
            # Create export package
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                # Add workspace configuration
                workspace_json = json.dumps(asdict(workspace), indent=2, default=str)
                zf.writestr('workspace.json', workspace_json)
                
                # Add README
                readme_content = f"""
# Lionaire Workspace Export: {workspace.name}

## Description
{workspace.description}

## Configuration Details
- Pair: {workspace.pair}
- Timeframe: {workspace.timeframe}
- Days Back: {workspace.days_back}
- Indicators: {len(workspace.indicators)}
- Created: {workspace.created_at}
- Author: {workspace.author}

## Import Instructions
1. Open Lionaire Trading Platform
2. Go to Configuration Manager
3. Select "Import Workspace"
4. Choose this file
5. Follow the import wizard

Generated by Lionaire Trading Platform
Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """.strip()
                zf.writestr('README.txt', readme_content)
            
            file_size_mb = output_path.stat().st_size / 1024 / 1024
            export_time = (datetime.now() - start_time).total_seconds()
            
            result = ConfigExportResult(
                success=True,
                file_path=output_path,
                file_size_mb=file_size_mb,
                export_time=export_time,
                items_exported=1
            )
            
            logger.info(f"Workspace exported: {output_path}")
            return result
            
        except Exception as e:
            export_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Workspace export failed: {e}")
            
            return ConfigExportResult(
                success=False,
                file_path=None,
                file_size_mb=0,
                export_time=export_time,
                items_exported=0,
                error_message=str(e)
            )
    
    def import_workspace(self, import_path: Path) -> bool:
        """Import workspace from package."""
        try:
            if not import_path.exists():
                raise ValueError(f"Import file not found: {import_path}")
            
            with zipfile.ZipFile(import_path, 'r') as zf:
                # Read workspace configuration
                workspace_json = zf.read('workspace.json').decode('utf-8')
                workspace_data = json.loads(workspace_json)
                
                # Convert datetime strings
                workspace_data['created_at'] = datetime.fromisoformat(workspace_data['created_at'])
                workspace_data['modified_at'] = datetime.now()  # Update modification time
                
                # Save imported workspace
                workspace_file = self.workspaces_dir / f"{workspace_data['name'].replace(' ', '_')}.json"
                with open(workspace_file, 'w', encoding='utf-8') as f:
                    json.dump(workspace_data, f, indent=2, default=str)
            
            logger.info(f"Workspace imported: {workspace_data['name']}")
            return True
            
        except Exception as e:
            logger.error(f"Workspace import failed: {e}")
            return False
