"""
Comprehensive Data Exporter for Lionaire platform.
Phase 4.2.2 - Comprehensive Data Export.

Features:
- CSV with custom formatting
- JSON with metadata
- Excel with multiple sheets
- Parquet for efficient storage
- Date range selection
- Indicator data inclusion
- Custom column selection
- Compression options
"""

import pandas as pd
import json
import gzip
import zipfile
from typing import Dict, List, Optional, Any, Tuple, Union
from pathlib import Path
from datetime import datetime, date
from dataclasses import dataclass
import io

from ..config.logging_config import get_logger
from ..config.user_settings import get_user_settings
from ..indicators.indicator_manager import IndicatorResult

logger = get_logger(__name__)


@dataclass
class DataExportSettings:
    """Data export settings."""
    format: str  # 'csv', 'json', 'excel', 'parquet'
    include_indicators: bool = True
    include_metadata: bool = True
    date_range: Optional[Tuple[date, date]] = None
    columns: Optional[List[str]] = None  # Specific columns to export
    compression: Optional[str] = None  # 'gzip', 'zip', 'bz2'
    decimal_places: int = 4
    date_format: str = '%Y-%m-%d %H:%M:%S'
    timezone: Optional[str] = None
    custom_headers: Optional[Dict[str, str]] = None


@dataclass
class DataExportResult:
    """Data export result."""
    success: bool
    file_path: Optional[Path]
    file_size_mb: float
    rows_exported: int
    columns_exported: int
    export_time: float
    format: str
    compression: Optional[str]
    error_message: Optional[str] = None


class DataExporter:
    """
    Comprehensive data exporter with multiple formats and customization.
    """
    
    def __init__(self):
        """Initialize Data Exporter."""
        self.user_settings = get_user_settings()
        
        # Supported formats
        self.supported_formats = ['csv', 'json', 'excel', 'parquet']
        self.supported_compressions = ['gzip', 'zip', 'bz2']
        
        # Default column mappings
        self.column_mappings = {
            'datetime': 'Timestamp',
            'open': 'Open',
            'high': 'High', 
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }
        
        logger.info("Data Exporter initialized")
    
    def export_data(
        self,
        data: pd.DataFrame,
        indicators: Optional[Dict[str, IndicatorResult]] = None,
        settings: DataExportSettings = None,
        output_path: Optional[Path] = None
    ) -> DataExportResult:
        """
        Export data with specified settings.
        
        Args:
            data: OHLCV DataFrame to export
            indicators: Optional indicator results
            settings: Export settings
            output_path: Output file path (optional)
            
        Returns:
            Export result
        """
        start_time = datetime.now()
        
        try:
            if settings is None:
                settings = DataExportSettings(format='csv')
            
            # Validate format
            if settings.format not in self.supported_formats:
                raise ValueError(f"Unsupported format: {settings.format}")
            
            # Prepare data
            prepared_data = self._prepare_data(data, indicators, settings)
            
            # Generate output path if not provided
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                extension = settings.format
                if settings.compression:
                    extension += f".{settings.compression}"
                filename = f"data_export_{timestamp}.{extension}"
                output_path = Path(filename)
            
            # Export based on format
            if settings.format == 'csv':
                success = self._export_csv(prepared_data, settings, output_path)
            elif settings.format == 'json':
                success = self._export_json(prepared_data, settings, output_path)
            elif settings.format == 'excel':
                success = self._export_excel(prepared_data, indicators, settings, output_path)
            elif settings.format == 'parquet':
                success = self._export_parquet(prepared_data, settings, output_path)
            else:
                raise ValueError(f"Export method not implemented for: {settings.format}")
            
            if not success:
                raise Exception("Export failed")
            
            # Calculate metrics
            file_size_mb = output_path.stat().st_size / 1024 / 1024 if output_path.exists() else 0
            export_time = (datetime.now() - start_time).total_seconds()
            
            result = DataExportResult(
                success=True,
                file_path=output_path,
                file_size_mb=file_size_mb,
                rows_exported=len(prepared_data),
                columns_exported=len(prepared_data.columns),
                export_time=export_time,
                format=settings.format,
                compression=settings.compression
            )
            
            logger.info(f"Data exported successfully: {output_path} "
                       f"({len(prepared_data)} rows, {file_size_mb:.2f} MB)")
            return result
            
        except Exception as e:
            export_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Data export failed: {e}")
            
            return DataExportResult(
                success=False,
                file_path=None,
                file_size_mb=0,
                rows_exported=0,
                columns_exported=0,
                export_time=export_time,
                format=settings.format if settings else 'unknown',
                compression=settings.compression if settings else None,
                error_message=str(e)
            )
    
    def _prepare_data(
        self,
        data: pd.DataFrame,
        indicators: Optional[Dict[str, IndicatorResult]],
        settings: DataExportSettings
    ) -> pd.DataFrame:
        """Prepare data for export."""
        try:
            # Start with a copy of the data
            prepared_data = data.copy()
            
            # Apply date range filter
            if settings.date_range:
                start_date, end_date = settings.date_range
                mask = (prepared_data.index.date >= start_date) & (prepared_data.index.date <= end_date)
                prepared_data = prepared_data[mask]
            
            # Add indicators if requested
            if settings.include_indicators and indicators:
                for name, indicator_result in indicators.items():
                    if hasattr(indicator_result, 'data') and indicator_result.data is not None:
                        # Handle different indicator data types
                        if isinstance(indicator_result.data, pd.Series):
                            prepared_data[f"Indicator_{name}"] = indicator_result.data
                        elif isinstance(indicator_result.data, pd.DataFrame):
                            # For multi-column indicators
                            for col in indicator_result.data.columns:
                                prepared_data[f"Indicator_{name}_{col}"] = indicator_result.data[col]
            
            # Apply column selection
            if settings.columns:
                available_columns = [col for col in settings.columns if col in prepared_data.columns]
                if available_columns:
                    prepared_data = prepared_data[available_columns]
            
            # Apply custom headers
            if settings.custom_headers:
                prepared_data = prepared_data.rename(columns=settings.custom_headers)
            else:
                # Apply default column mappings
                prepared_data = prepared_data.rename(columns=self.column_mappings)
            
            # Round numeric columns
            numeric_columns = prepared_data.select_dtypes(include=['float64', 'float32']).columns
            prepared_data[numeric_columns] = prepared_data[numeric_columns].round(settings.decimal_places)
            
            return prepared_data
            
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            return data
    
    def _export_csv(self, data: pd.DataFrame, settings: DataExportSettings, output_path: Path) -> bool:
        """Export as CSV."""
        try:
            # Prepare CSV options
            csv_options = {
                'index': True,
                'date_format': settings.date_format,
                'float_format': f'%.{settings.decimal_places}f'
            }
            
            # Handle compression
            if settings.compression == 'gzip':
                with gzip.open(output_path, 'wt', encoding='utf-8') as f:
                    data.to_csv(f, **csv_options)
            elif settings.compression == 'zip':
                with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                    csv_name = output_path.stem + '.csv'
                    with zf.open(csv_name, 'w') as f:
                        csv_content = data.to_csv(**csv_options)
                        f.write(csv_content.encode('utf-8'))
            else:
                data.to_csv(output_path, **csv_options)
            
            return True
            
        except Exception as e:
            logger.error(f"CSV export failed: {e}")
            return False
    
    def _export_json(self, data: pd.DataFrame, settings: DataExportSettings, output_path: Path) -> bool:
        """Export as JSON with metadata."""
        try:
            # Prepare JSON data
            json_data = {
                'metadata': {
                    'export_timestamp': datetime.now().isoformat(),
                    'rows': len(data),
                    'columns': len(data.columns),
                    'date_range': {
                        'start': data.index.min().isoformat() if not data.empty else None,
                        'end': data.index.max().isoformat() if not data.empty else None
                    },
                    'timezone': settings.timezone,
                    'decimal_places': settings.decimal_places
                } if settings.include_metadata else {},
                'data': data.to_dict('records')
            }
            
            # Convert timestamps to strings
            for record in json_data['data']:
                if 'Timestamp' in record:
                    record['Timestamp'] = record['Timestamp'].strftime(settings.date_format)
            
            # Handle compression
            if settings.compression == 'gzip':
                with gzip.open(output_path, 'wt', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, default=str)
            elif settings.compression == 'zip':
                with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                    json_name = output_path.stem + '.json'
                    with zf.open(json_name, 'w') as f:
                        json_content = json.dumps(json_data, indent=2, default=str)
                        f.write(json_content.encode('utf-8'))
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, default=str)
            
            return True
            
        except Exception as e:
            logger.error(f"JSON export failed: {e}")
            return False
    
    def _export_excel(
        self,
        data: pd.DataFrame,
        indicators: Optional[Dict[str, IndicatorResult]],
        settings: DataExportSettings,
        output_path: Path
    ) -> bool:
        """Export as Excel with multiple sheets."""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Main data sheet
                data.to_excel(writer, sheet_name='Data', index=True)
                
                # Indicators sheet (if available)
                if settings.include_indicators and indicators:
                    indicator_summary = []
                    for name, indicator_result in indicators.items():
                        if hasattr(indicator_result, 'parameters'):
                            indicator_summary.append({
                                'Indicator': name,
                                'Type': getattr(indicator_result, 'indicator_type', 'Unknown'),
                                'Parameters': str(getattr(indicator_result, 'parameters', {}))
                            })
                    
                    if indicator_summary:
                        pd.DataFrame(indicator_summary).to_excel(
                            writer, sheet_name='Indicators', index=False
                        )
                
                # Metadata sheet
                if settings.include_metadata:
                    metadata = {
                        'Export Information': [
                            f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                            f"Rows Exported: {len(data)}",
                            f"Columns Exported: {len(data.columns)}",
                            f"Date Range: {data.index.min()} to {data.index.max()}" if not data.empty else "No data",
                            f"Decimal Places: {settings.decimal_places}",
                            f"Timezone: {settings.timezone or 'Not specified'}"
                        ]
                    }
                    
                    pd.DataFrame(metadata).to_excel(
                        writer, sheet_name='Metadata', index=False, header=False
                    )
            
            return True
            
        except Exception as e:
            logger.error(f"Excel export failed: {e}")
            return False
    
    def _export_parquet(self, data: pd.DataFrame, settings: DataExportSettings, output_path: Path) -> bool:
        """Export as Parquet."""
        try:
            # Determine compression
            compression = 'snappy'  # Default
            if settings.compression == 'gzip':
                compression = 'gzip'
            elif settings.compression == 'bz2':
                compression = 'brotli'  # Parquet doesn't support bz2, use brotli instead
            
            data.to_parquet(output_path, compression=compression, index=True)
            return True
            
        except Exception as e:
            logger.error(f"Parquet export failed: {e}")
            return False
