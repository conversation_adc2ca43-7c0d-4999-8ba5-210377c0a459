"""
Unified Export Manager for Lionaire platform.
Phase 4.2 - Export & Import Enhancement.

Features:
- Unified interface for all export operations
- Batch export capabilities
- Export templates and presets
- Progress tracking and reporting
"""

import asyncio
import threading
from typing import Dict, List, Optional, Any, Union, Callable
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
import plotly.graph_objects as go
import pandas as pd

from ..config.logging_config import get_logger
from .chart_exporter import ChartExporter, ExportSettings as ChartExportSettings
from .data_exporter import DataExporter, DataExportSettings
from .config_manager import ConfigurationManager
from ..indicators.indicator_manager import IndicatorResult

logger = get_logger(__name__)


@dataclass
class BatchExportJob:
    """Batch export job definition."""
    job_id: str
    job_type: str  # 'chart', 'data', 'workspace'
    items: List[Any]
    settings: Any
    output_dir: Path
    progress_callback: Optional[Callable] = None


@dataclass
class ExportProgress:
    """Export progress tracking."""
    job_id: str
    total_items: int
    completed_items: int
    failed_items: int
    current_item: str
    progress_percent: float
    estimated_time_remaining: float
    status: str  # 'running', 'completed', 'failed', 'cancelled'


class ExportManager:
    """
    Unified export manager with batch processing and progress tracking.
    """
    
    def __init__(self):
        """Initialize Export Manager."""
        # Initialize sub-managers
        self.chart_exporter = ChartExporter()
        self.data_exporter = DataExporter()
        self.config_manager = ConfigurationManager()
        
        # Job tracking
        self.active_jobs: Dict[str, BatchExportJob] = {}
        self.job_progress: Dict[str, ExportProgress] = {}
        self.job_results: Dict[str, List[Any]] = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Export templates
        self.export_templates = {
            'professional_report': {
                'chart': ChartExportSettings(
                    format='png',
                    width=1920,
                    height=1080,
                    dpi=300,
                    theme='plotly_white',
                    watermark='Lionaire Trading Platform'
                ),
                'data': DataExportSettings(
                    format='excel',
                    include_indicators=True,
                    include_metadata=True,
                    decimal_places=4
                )
            },
            'web_presentation': {
                'chart': ChartExportSettings(
                    format='html',
                    width=1200,
                    height=800,
                    theme='plotly_white',
                    include_data=True
                ),
                'data': DataExportSettings(
                    format='json',
                    include_indicators=True,
                    include_metadata=True,
                    compression='gzip'
                )
            },
            'data_analysis': {
                'chart': ChartExportSettings(
                    format='svg',
                    width=1600,
                    height=900,
                    theme='plotly_white'
                ),
                'data': DataExportSettings(
                    format='csv',
                    include_indicators=True,
                    decimal_places=6
                )
            },
            'quick_share': {
                'chart': ChartExportSettings(
                    format='png',
                    width=800,
                    height=600,
                    dpi=150,
                    theme='plotly_white'
                ),
                'data': DataExportSettings(
                    format='csv',
                    include_indicators=False,
                    decimal_places=4
                )
            }
        }
        
        logger.info("Export Manager initialized")
    
    def export_chart(
        self,
        figure: go.Figure,
        settings: Optional[ChartExportSettings] = None,
        template: Optional[str] = None,
        output_path: Optional[Path] = None
    ):
        """Export single chart."""
        try:
            # Apply template if specified
            if template and template in self.export_templates:
                settings = self.export_templates[template]['chart']
            elif settings is None:
                settings = ChartExportSettings(format='png')
            
            return self.chart_exporter.export_chart(figure, settings, output_path)
            
        except Exception as e:
            logger.error(f"Chart export failed: {e}")
            return None
    
    def export_data(
        self,
        data: pd.DataFrame,
        indicators: Optional[Dict[str, IndicatorResult]] = None,
        settings: Optional[DataExportSettings] = None,
        template: Optional[str] = None,
        output_path: Optional[Path] = None
    ):
        """Export single dataset."""
        try:
            # Apply template if specified
            if template and template in self.export_templates:
                settings = self.export_templates[template]['data']
            elif settings is None:
                settings = DataExportSettings(format='csv')
            
            return self.data_exporter.export_data(data, indicators, settings, output_path)
            
        except Exception as e:
            logger.error(f"Data export failed: {e}")
            return None
    
    def export_workspace(self, workspace_name: str, output_path: Optional[Path] = None):
        """Export workspace configuration."""
        try:
            return self.config_manager.export_workspace(workspace_name, output_path)
            
        except Exception as e:
            logger.error(f"Workspace export failed: {e}")
            return None
    
    def start_batch_export(
        self,
        job_type: str,
        items: List[Any],
        settings: Any,
        output_dir: Path,
        progress_callback: Optional[Callable] = None
    ) -> str:
        """Start batch export job."""
        try:
            # Generate job ID
            job_id = f"export_{job_type}_{int(datetime.now().timestamp())}"
            
            # Create job
            job = BatchExportJob(
                job_id=job_id,
                job_type=job_type,
                items=items,
                settings=settings,
                output_dir=output_dir,
                progress_callback=progress_callback
            )
            
            # Initialize progress tracking
            progress = ExportProgress(
                job_id=job_id,
                total_items=len(items),
                completed_items=0,
                failed_items=0,
                current_item="",
                progress_percent=0.0,
                estimated_time_remaining=0.0,
                status="running"
            )
            
            with self._lock:
                self.active_jobs[job_id] = job
                self.job_progress[job_id] = progress
                self.job_results[job_id] = []
            
            # Start processing in background thread
            thread = threading.Thread(
                target=self._process_batch_job,
                args=(job,),
                daemon=True
            )
            thread.start()
            
            logger.info(f"Batch export job started: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to start batch export: {e}")
            return ""
    
    def _process_batch_job(self, job: BatchExportJob):
        """Process batch export job."""
        try:
            start_time = datetime.now()
            
            for i, item in enumerate(job.items):
                try:
                    # Update progress
                    with self._lock:
                        progress = self.job_progress[job.job_id]
                        progress.current_item = str(item)
                        progress.progress_percent = (i / len(job.items)) * 100
                        
                        # Estimate time remaining
                        elapsed_time = (datetime.now() - start_time).total_seconds()
                        if i > 0:
                            avg_time_per_item = elapsed_time / i
                            remaining_items = len(job.items) - i
                            progress.estimated_time_remaining = avg_time_per_item * remaining_items
                    
                    # Call progress callback
                    if job.progress_callback:
                        job.progress_callback(progress)
                    
                    # Process item based on job type
                    if job.job_type == 'chart':
                        result = self._process_chart_item(item, job)
                    elif job.job_type == 'data':
                        result = self._process_data_item(item, job)
                    elif job.job_type == 'workspace':
                        result = self._process_workspace_item(item, job)
                    else:
                        raise ValueError(f"Unknown job type: {job.job_type}")
                    
                    # Store result
                    with self._lock:
                        self.job_results[job.job_id].append(result)
                        
                        if result and getattr(result, 'success', False):
                            self.job_progress[job.job_id].completed_items += 1
                        else:
                            self.job_progress[job.job_id].failed_items += 1
                    
                except Exception as e:
                    logger.error(f"Error processing item {i}: {e}")
                    with self._lock:
                        self.job_progress[job.job_id].failed_items += 1
            
            # Mark job as completed
            with self._lock:
                progress = self.job_progress[job.job_id]
                progress.status = "completed"
                progress.progress_percent = 100.0
                progress.estimated_time_remaining = 0.0
                progress.current_item = "Completed"
            
            # Final progress callback
            if job.progress_callback:
                job.progress_callback(progress)
            
            logger.info(f"Batch export job completed: {job.job_id}")
            
        except Exception as e:
            logger.error(f"Batch export job failed: {e}")
            with self._lock:
                if job.job_id in self.job_progress:
                    self.job_progress[job.job_id].status = "failed"
    
    def _process_chart_item(self, item: Tuple[go.Figure, str], job: BatchExportJob):
        """Process single chart item."""
        try:
            figure, name = item
            output_path = job.output_dir / f"{name}.{job.settings.format}"
            return self.chart_exporter.export_chart(figure, job.settings, output_path)
            
        except Exception as e:
            logger.error(f"Error processing chart item: {e}")
            return None
    
    def _process_data_item(self, item: Tuple[pd.DataFrame, str, Optional[Dict]], job: BatchExportJob):
        """Process single data item."""
        try:
            if len(item) == 3:
                data, name, indicators = item
            else:
                data, name = item
                indicators = None
            
            output_path = job.output_dir / f"{name}.{job.settings.format}"
            return self.data_exporter.export_data(data, indicators, job.settings, output_path)
            
        except Exception as e:
            logger.error(f"Error processing data item: {e}")
            return None
    
    def _process_workspace_item(self, item: str, job: BatchExportJob):
        """Process single workspace item."""
        try:
            workspace_name = item
            output_path = job.output_dir / f"{workspace_name}.zip"
            return self.config_manager.export_workspace(workspace_name, output_path)
            
        except Exception as e:
            logger.error(f"Error processing workspace item: {e}")
            return None
    
    def get_job_progress(self, job_id: str) -> Optional[ExportProgress]:
        """Get job progress."""
        with self._lock:
            return self.job_progress.get(job_id)
    
    def get_job_results(self, job_id: str) -> List[Any]:
        """Get job results."""
        with self._lock:
            return self.job_results.get(job_id, [])
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel running job."""
        try:
            with self._lock:
                if job_id in self.job_progress:
                    self.job_progress[job_id].status = "cancelled"
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling job: {e}")
            return False
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """Clean up old completed jobs."""
        try:
            cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
            
            with self._lock:
                jobs_to_remove = []
                
                for job_id in self.active_jobs:
                    # Extract timestamp from job ID
                    try:
                        job_timestamp = float(job_id.split('_')[-1])
                        if job_timestamp < cutoff_time:
                            progress = self.job_progress.get(job_id)
                            if progress and progress.status in ['completed', 'failed', 'cancelled']:
                                jobs_to_remove.append(job_id)
                    except:
                        pass  # Skip malformed job IDs
                
                # Remove old jobs
                for job_id in jobs_to_remove:
                    self.active_jobs.pop(job_id, None)
                    self.job_progress.pop(job_id, None)
                    self.job_results.pop(job_id, None)
                
                if jobs_to_remove:
                    logger.info(f"Cleaned up {len(jobs_to_remove)} old export jobs")
                    
        except Exception as e:
            logger.error(f"Error cleaning up jobs: {e}")
    
    def get_export_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get available export templates."""
        return {
            name: {
                'chart_settings': template['chart'].__dict__,
                'data_settings': template['data'].__dict__
            }
            for name, template in self.export_templates.items()
        }
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """Get export statistics."""
        try:
            with self._lock:
                total_jobs = len(self.job_progress)
                completed_jobs = sum(1 for p in self.job_progress.values() if p.status == 'completed')
                failed_jobs = sum(1 for p in self.job_progress.values() if p.status == 'failed')
                running_jobs = sum(1 for p in self.job_progress.values() if p.status == 'running')
                
                total_items = sum(p.total_items for p in self.job_progress.values())
                completed_items = sum(p.completed_items for p in self.job_progress.values())
                failed_items = sum(p.failed_items for p in self.job_progress.values())
                
                return {
                    'jobs': {
                        'total': total_jobs,
                        'completed': completed_jobs,
                        'failed': failed_jobs,
                        'running': running_jobs
                    },
                    'items': {
                        'total': total_items,
                        'completed': completed_items,
                        'failed': failed_items,
                        'success_rate': (completed_items / total_items * 100) if total_items > 0 else 0
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting export statistics: {e}")
            return {}
